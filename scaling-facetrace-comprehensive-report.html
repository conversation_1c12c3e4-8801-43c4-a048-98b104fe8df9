<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scaling FaceTrace - It's Highway Robbery</title>
    <meta name="description" content="Comprehensive presentation on FaceTrace scaling strategy and B2B opportunities">
    <meta name="author" content="Bryce Bayens">
    
    <style>
        :root {
            --primary: #00d4ff;
            --secondary: #ff6b6b;
            --accent: #4ecdc4;
            --purple: #9b59b6;
            --orange: #f39c12;
            --green: #27ae60;
            --dark: #0a0a0a;
            --dark-light: #1a1a1a;
            --dark-lighter: #2a2a2a;
            --text: #ffffff;
            --text-dim: #a0a0a0;
            --gradient-1: linear-gradient(135deg, #00d4ff, #ff6b6b);
            --gradient-2: linear-gradient(135deg, #4ecdc4, #556270);
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            --shadow-lg: 0 20px 50px rgba(0, 0, 0, 0.7);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--dark);
            color: var(--text);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--dark);
            overflow: hidden;
        }

        .bg-animation::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--primary) 0%, transparent 70%);
            opacity: 0.05;
            animation: rotate 30s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(26, 26, 26, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        nav ul {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        nav a {
            color: var(--text-dim);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
            display: block;
            position: relative;
            overflow: hidden;
        }

        nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-1);
            transition: left 0.3s ease;
            z-index: -1;
        }

        nav a:hover::before,
        nav a.active::before {
            left: 0;
        }

        nav a:hover,
        nav a.active {
            color: var(--text);
            transform: translateX(5px);
        }

        /* Progress Bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: var(--gradient-1);
            z-index: 1001;
            transition: width 0.3s ease;
        }

        /* Page Number Counter */
        .page-counter {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(26, 26, 26, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px 20px;
            box-shadow: var(--shadow);
            font-weight: 600;
            color: var(--primary);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            text-align: center;
        }

        .hero-content {
            max-width: 1200px;
            animation: fadeInUp 1s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero h1 {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 900;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            line-height: 1.1;
        }

        .hero h2 {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            color: var(--secondary);
            margin-bottom: 2rem;
            font-weight: 700;
        }

        .hero-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            color: var(--text-dim);
            font-size: 1.1rem;
            margin-bottom: 3rem;
        }

        .hero-meta span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Cover Page Animation */
        .cover-animation {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .fade-step {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .fade-step.active {
            opacity: 1;
            transform: translateY(0);
        }

        .logo-variations {
            margin: 3rem 0;
        }

        .logo-variation {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 15px;
            background: rgba(26, 26, 26, 0.8);
            border: 2px solid transparent;
            transition: all 0.5s ease;
        }

        .logo-variation.fed {
            background: linear-gradient(135deg, var(--secondary), var(--orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-variation.state {
            background: linear-gradient(135deg, var(--accent), var(--green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-variation.county {
            background: linear-gradient(135deg, var(--purple), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .surprise-button {
            background: var(--gradient-1);
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 50px;
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .surprise-button:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        /* Sections */
        section {
            padding: 5rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: clamp(2rem, 5vw, 3rem);
            margin-bottom: 1rem;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-header p {
            font-size: 1.2rem;
            color: var(--text-dim);
            max-width: 800px;
            margin: 0 auto;
        }

        /* Cards */
        .card {
            background: var(--dark-light);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--dark-lighter);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent), var(--purple));
            border-radius: 20px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .card:hover::before {
            opacity: 1;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .card h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        /* Grid Layouts */
        .grid {
            display: grid;
            gap: 2rem;
            margin-top: 3rem;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        /* Data Sources Grid */
        .data-sources {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .data-source-card {
            background: var(--dark-lighter);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid transparent;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
            overflow: hidden;
        }

        .data-source-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));
            border-radius: 20px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .data-source-card:hover::before {
            opacity: 1;
        }

        .data-source-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .data-source-card h4 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .accuracy {
            color: var(--accent);
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0.5rem 0;
        }

        .price {
            color: var(--orange);
            font-weight: 600;
            font-size: 1rem;
        }

        .description {
            color: var(--text-dim);
            font-size: 0.9rem;
            line-height: 1.4;
            margin-top: 1rem;
        }

        /* Stats */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .stat-card {
            background: var(--dark-lighter);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            border: 2px solid var(--dark-lighter);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(0, 212, 255, 0.1));
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .stat-card:hover::before {
            transform: translateX(0);
        }

        .stat-card:hover {
            transform: scale(1.05);
            border-color: var(--primary);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }

        .stat-label {
            color: var(--text-dim);
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        /* Feature List */
        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 1rem 0;
            border-bottom: 1px solid var(--dark-lighter);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .feature-list li:hover {
            padding-left: 1rem;
            color: var(--primary);
        }

        .feature-list li::before {
            content: '✓';
            color: var(--accent);
            font-size: 1.5rem;
            font-weight: bold;
        }

        /* Architecture Diagram */
        .architecture-diagram {
            background: var(--dark-lighter);
            border-radius: 20px;
            padding: 3rem;
            margin: 3rem 0;
            text-align: center;
        }

        .flow-diagram {
            display: flex;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
            justify-content: center;
            margin: 2rem 0;
        }

        .flow-item {
            background: var(--dark-light);
            border: 2px solid var(--primary);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .flow-item:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .flow-arrow {
            width: 40px;
            height: 2px;
            background: var(--primary);
            position: relative;
        }

        .flow-arrow::after {
            content: '';
            position: absolute;
            right: -5px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid var(--primary);
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }

        /* Timeline */
        .timeline {
            position: relative;
            padding: 2rem 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--primary);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            width: 50%;
            padding: 2rem;
            opacity: 0;
            transform: translateX(-30px);
            transition: all 0.6s ease;
        }

        .timeline-item.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            text-align: right;
            padding-right: 3rem;
        }

        .timeline-item:nth-child(even) {
            left: 50%;
            padding-left: 3rem;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: var(--primary);
            border-radius: 50%;
            top: 2rem;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .timeline-item:nth-child(odd)::after {
            right: -10px;
        }

        .timeline-item:nth-child(even)::after {
            left: -10px;
        }

        /* Enhanced Puzzle Section */
        .puzzle-section {
            text-align: center;
            margin: 4rem 0;
        }

        .puzzle-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            max-width: 800px;
            margin: 3rem auto;
        }

        .puzzle-piece {
            background: var(--dark-lighter);
            border: 3px solid var(--primary);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            opacity: 0;
            transform: scale(0.8) rotate(10deg);
            transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .puzzle-piece.animate {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        .puzzle-piece h4 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .puzzle-piece p {
            color: var(--text-dim);
            font-size: 0.9rem;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--gradient-1);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            nav {
                right: 10px;
                top: 10px;
                padding: 15px;
            }

            .page-counter {
                left: 10px;
                top: 10px;
                padding: 10px 15px;
            }

            .timeline::before {
                left: 30px;
            }

            .timeline-item {
                width: 100%;
                left: 0 !important;
                padding-left: 60px !important;
                text-align: left !important;
            }

            .timeline-item::after {
                left: 20px !important;
            }

            .flow-diagram {
                flex-direction: column;
            }

            .flow-arrow {
                transform: rotate(90deg);
                margin: 1rem 0;
            }

            .puzzle-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .data-sources {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        /* Counter Animation */
        .counter {
            display: inline-block;
        }

        /* Presentation Mode Styles */
        .presentation-mode {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: var(--dark);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .presentation-mode.active {
            display: flex;
        }

        .slide {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.5s ease;
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide-controls {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 2001;
        }

        .slide-nav {
            background: rgba(26, 26, 26, 0.9);
            border: 2px solid var(--primary);
            color: var(--primary);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slide-nav:hover {
            background: var(--primary);
            color: var(--dark);
        }

        /* Loading Animation */
        .loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--dark);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loader.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loader-circle {
            width: 50px;
            height: 50px;
            border: 3px solid var(--dark-lighter);
            border-top-color: var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loader" id="loader">
        <div class="loader-circle"></div>
    </div>

    <!-- Animated Background -->
    <div class="bg-animation"></div>

    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Page Counter -->
    <div class="page-counter">
        <span id="current-page">Page 1</span>
    </div>

    <!-- Navigation -->
    <nav id="navigation">
        <ul>
            <li><a href="#home" class="nav-link active">Home</a></li>
            <li><a href="#executive-summary" class="nav-link">Executive Summary</a></li>
            <li><a href="#data-puzzle" class="nav-link">Data Puzzle</a></li>
            <li><a href="#architecture" class="nav-link">Architecture</a></li>
            <li><a href="#image-enhancement" class="nav-link">Enhancement</a></li>
            <li><a href="#data-sources" class="nav-link">Data Sources</a></li>
            <li><a href="#government" class="nav-link">Government</a></li>
            <li><a href="#b2b-opportunities" class="nav-link">B2B Opportunities</a></li>
            <li><a href="#financial" class="nav-link">Financial</a></li>
            <li><a href="#implementation" class="nav-link">Implementation</a></li>
        </ul>
    </nav>

    <!-- Cover Page / Hero Section -->
    <section class="hero" id="home">
        <div class="cover-animation">
            <div class="fade-step active">
                <h1 style="font-size: 5rem; margin-bottom: 1rem;">FaceTrace</h1>
                <p style="font-size: 1.5rem; color: var(--text-dim); margin-bottom: 2rem;">2.4 Billion Faces Under Management</p>
            </div>

            <div class="fade-step" id="author-fade">
                <h2>Bryce Bayens | June 2025 | FaceTrace.pro</h2>
            </div>

            <div class="fade-step" id="logo-variations" style="display: none;">
                <div style="font-size: 3rem; margin: 2rem 0; background: var(--gradient-1); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    FaceTraceGov
                </div>
                <div class="logo-variations">
                    <div class="logo-variation fed">1. FedTrace</div>
                    <div class="logo-variation state">2. StateTrace</div>
                    <div class="logo-variation county">3. CountyTrace</div>
                </div>
                <div style="font-size: 1.8rem; color: var(--secondary); margin-top: 2rem; font-weight: 700;">
                    "Suspect Identification"
                </div>
            </div>

            <div class="fade-step" id="surprise-section" style="display: none;">
                <button class="surprise-button" onclick="startPresentation()">
                    Surprise Me
                </button>
            </div>
        </div>
    </section>

    <!-- Executive Summary -->
    <section id="executive-summary">
        <div class="section-header">
            <h2>What is the most valuable asset in the world?</h2>
            <p style="font-size: 4rem; margin: 2rem 0; background: var(--gradient-1); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 900;">DATA</p>
        </div>

        <div class="grid grid-2">
            <div class="card">
                <h3 style="color: var(--secondary);">The Problem</h3>
                <p style="font-size: 1.3rem; margin: 1rem 0;">Agencies crowdsource information to identify suspects.</p>
                <p style="font-size: 1.5rem; color: var(--secondary); text-align: center; margin-top: 2rem;">⏰ Time is ticking</p>
            </div>

            <div class="card">
                <h3 style="color: var(--accent);">The Vision</h3>
                <p style="font-size: 1.5rem; font-weight: 700; color: var(--accent);">Make America Safe Again</p>
            </div>
        </div>

        <div class="card">
            <h3>The Solution</h3>
            <p style="font-size: 1.4rem; text-align: center; margin: 2rem 0; color: var(--accent);">
                Data is a puzzle. <span style="color: var(--primary); font-weight: 700;">Piece the puzzle.</span>
            </p>
            <div style="text-align: center; margin: 3rem 0;">
                <div style="font-size: 3rem; background: var(--gradient-2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 900;">
                    FaceTraceGov
                </div>
                <div style="font-size: 2rem; color: var(--secondary); font-weight: 700; margin-top: 1rem;">
                    It's Highway Robbery
                </div>
            </div>
        </div>
    </section>

    <!-- Data Puzzle Section -->
    <section id="data-puzzle">
        <div class="section-header">
            <h2>The FaceTraceGov Data Puzzle</h2>
            <p>Piecing together data sources for comprehensive suspect identification</p>
        </div>

        <div class="puzzle-section">
            <div class="puzzle-grid" id="puzzle-grid">
                <div class="puzzle-piece" data-delay="0">
                    <h4>TLO (TransUnion)</h4>
                    <p class="accuracy">96% accuracy</p>
                    <p class="price">$1.80/search</p>
                    <p class="description">Advanced skip tracing algorithms</p>
                </div>
                <div class="puzzle-piece" data-delay="200">
                    <h4>IDI Core</h4>
                    <p class="accuracy">88% accuracy</p>
                    <p class="price">$0.50/search</p>
                    <p class="description">Profiles on every U.S. adult</p>
                </div>
                <div class="puzzle-piece" data-delay="400">
                    <h4>Tracers</h4>
                    <p class="accuracy">98% U.S. coverage</p>
                    <p class="price">Professional rates</p>
                    <p class="description">2,000+ law enforcement users</p>
                </div>
                <div class="puzzle-piece" data-delay="600">
                    <h4>Clear (Thomson Reuters)</h4>
                    <p class="accuracy">Professional-grade</p>
                    <p class="price">Enterprise licensing</p>
                    <p class="description">Comprehensive public records</p>
                </div>
                <div class="puzzle-piece" data-delay="800">
                    <h4>Accurint (LexisNexis)</h4>
                    <p class="accuracy">Extensive coverage</p>
                    <p class="price">Tiered pricing</p>
                    <p class="description">Enterprise-level security</p>
                </div>
                <div class="puzzle-piece" data-delay="1000">
                    <h4>Face Recognition AI</h4>
                    <p class="accuracy">Real-time processing</p>
                    <p class="price">Cloud-powered</p>
                    <p class="description">Enhanced image quality</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Architecture Plan -->
    <section id="architecture">
        <div class="section-header">
            <h2>FaceTrace Architecture Plan</h2>
            <p>Operating like advanced facial recognition search engines with enhanced capabilities</p>
        </div>

        <div class="architecture-diagram">
            <div class="flow-diagram">
                <div class="flow-item">
                    <h4>Image Input</h4>
                    <p>Security cameras, body cams, mobile uploads</p>
                </div>
                <div class="flow-arrow"></div>
                <div class="flow-item">
                    <h4>AI Enhancement</h4>
                    <p>Grainy image improvement, super-resolution</p>
                </div>
                <div class="flow-arrow"></div>
                <div class="flow-item">
                    <h4>Face Detection</h4>
                    <p>Multi-angle recognition, partial matching</p>
                </div>
                <div class="flow-arrow"></div>
                <div class="flow-item">
                    <h4>Data Fusion</h4>
                    <p>Skip tracing database integration</p>
                </div>
                <div class="flow-arrow"></div>
                <div class="flow-item">
                    <h4>Results</h4>
                    <p>Comprehensive suspect profiles</p>
                </div>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="card">
                <h3>Core Recognition Engine</h3>
                <ul class="feature-list">
                    <li>Real-time facial detection and encoding</li>
                    <li>Multi-angle recognition capability</li>
                    <li>Age progression algorithms</li>
                    <li>Partial face matching</li>
                    <li>Bias mitigation technology</li>
                </ul>
            </div>

            <div class="card">
                <h3>Search Aggregation</h3>
                <ul class="feature-list">
                    <li>Cross-platform identity matching</li>
                    <li>Social media integration</li>
                    <li>Public records correlation</li>
                    <li>News and media scanning</li>
                    <li>Criminal history cross-reference</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Image Enhancement Technologies -->
    <section id="image-enhancement">
        <div class="section-header">
            <h2>Image Enhancement Technologies</h2>
            <p>Transforming grainy security footage into actionable intelligence</p>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number">400%</span>
                <span class="stat-label">Resolution Increase</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">85%</span>
                <span class="stat-label">Success Rate</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">3s</span>
                <span class="stat-label">Processing Time</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">24/7</span>
                <span class="stat-label">Availability</span>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="card">
                <h3>AI-Powered Enhancement</h3>
                <ul class="feature-list">
                    <li>Super-resolution upscaling for low-quality images</li>
                    <li>Noise reduction and artifact removal</li>
                    <li>Motion blur correction and stabilization</li>
                    <li>Low-light enhancement and contrast optimization</li>
                    <li>Facial feature reconstruction from partial views</li>
                </ul>
            </div>

            <div class="card">
                <h3>Security Camera Optimization</h3>
                <ul class="feature-list">
                    <li>CCTV footage enhancement and clarification</li>
                    <li>Body camera video processing</li>
                    <li>Traffic camera image improvement</li>
                    <li>ATM and retail security footage enhancement</li>
                    <li>Mobile phone video stabilization and clarity</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Third-Party Data Sources -->
    <section id="data-sources">
        <div class="section-header">
            <h2>Third-Party Data Sources</h2>
            <p>Professional-grade databases for comprehensive investigations</p>
        </div>

        <div class="data-sources">
            <div class="data-source-card">
                <h4>TLO (TLOxp) - TransUnion</h4>
                <div class="accuracy">96% Accuracy Rate</div>
                <div class="price">$1.80 per search</div>
                <p class="description">Premium skip tracing database with comprehensive public records, credit headers, and identity verification. Trusted by law enforcement nationwide with proprietary linking algorithms.</p>
            </div>

            <div class="data-source-card">
                <h4>Tracers Information Services</h4>
                <div class="accuracy">Used by 2,000+ Law Enforcement</div>
                <div class="price">Professional Rates</div>
                <p class="description">#1 trusted cloud-based investigative software connecting to 98% of people in the U.S. with real-time data updates and advanced search capabilities.</p>
            </div>

            <div class="data-source-card">
                <h4>IDI Core (IDI Data)</h4>
                <div class="accuracy">88% Accuracy Rate</div>
                <div class="price">$0.50 per search (Most Cost-Effective)</div>
                <p class="description">Data fusion company with profiles on every U.S. adult. High-volume identity verification platform with extensive coverage of public records and commercial databases.</p>
            </div>

            <div class="data-source-card">
                <h4>Clear (Thomson Reuters)</h4>
                <div class="accuracy">Professional-Grade Platform</div>
                <div class="price">Enterprise Licensing</div>
                <p class="description">Comprehensive investigation platform used by law enforcement agencies worldwide, with advanced analytics, case management tools, and strong compliance features.</p>
            </div>

            <div class="data-source-card">
                <h4>Accurint (LexisNexis)</h4>
                <div class="accuracy">Extensive Database Coverage</div>
                <div class="price">Tiered Pricing Structure</div>
                <p class="description">Leading public records database with billions of records, advanced linking technology, and specialized law enforcement features with enterprise-level security.</p>
            </div>

            <div class="data-source-card">
                <h4>IRB Search</h4>
                <div class="accuracy">85+ Billion Records</div>
                <div class="price">$3 per search</div>
                <p class="description">Longest-running PI database designed specifically for collections industry with batch skip tracing functionality and multi-source aggregation capabilities.</p>
            </div>
        </div>
    </section>

    <!-- Government Integration -->
    <section id="government">
        <div class="section-header">
            <h2>Government Integration</h2>
            <p>FedTrace, StateTrace, and CountyTrace for comprehensive coverage</p>
        </div>

        <div class="grid grid-3">
            <div class="card">
                <h3 style="color: var(--secondary);">FedTrace</h3>
                <p>Federal agency integration for national security:</p>
                <ul class="feature-list">
                    <li>FBI CJIS compliance</li>
                    <li>Air-gapped deployment options</li>
                    <li>FIPS 140-2 encryption</li>
                    <li>Comprehensive audit logging</li>
                    <li>Multi-agency data sharing</li>
                </ul>
                <div class="stat-card" style="margin-top: 1rem;">
                    <span class="stat-number">$50M</span>
                    <span class="stat-label">Contract Potential</span>
                </div>
            </div>

            <div class="card">
                <h3 style="color: var(--accent);">StateTrace</h3>
                <p>State-level law enforcement integration:</p>
                <ul class="feature-list">
                    <li>State database integration</li>
                    <li>DMV photo matching</li>
                    <li>Missing persons alerts</li>
                    <li>Amber Alert system integration</li>
                    <li>Interstate data sharing protocols</li>
                </ul>
                <div class="stat-card" style="margin-top: 1rem;">
                    <span class="stat-number">50</span>
                    <span class="stat-label">States Available</span>
                </div>
            </div>

            <div class="card">
                <h3 style="color: var(--purple);">CountyTrace</h3>
                <p>Local law enforcement and municipal services:</p>
                <ul class="feature-list">
                    <li>Local PD integration</li>
                    <li>Court system access</li>
                    <li>Jail booking photos</li>
                    <li>Community alert systems</li>
                    <li>Mobile app for field officers</li>
                </ul>
                <div class="stat-card" style="margin-top: 1rem;">
                    <span class="stat-number">3,000+</span>
                    <span class="stat-label">Potential Counties</span>
                </div>
            </div>
        </div>
    </section>

    <!-- B2B Opportunities -->
    <section id="b2b-opportunities">
        <div class="section-header">
            <h2>B2B Revenue Opportunities</h2>
            <p>Skip tracing and investigation services for enterprise clients</p>
        </div>

        <div class="grid grid-2">
            <div class="card">
                <h3>Target Markets</h3>
                <ul class="feature-list">
                    <li>Private investigators and detective agencies</li>
                    <li>Law firms and legal professionals</li>
                    <li>Debt collection agencies</li>
                    <li>Insurance companies</li>
                    <li>Corporate security departments</li>
                    <li>Background check services</li>
                </ul>
            </div>

            <div class="card">
                <h3>Service Offerings</h3>
                <ul class="feature-list">
                    <li>API access for bulk searches</li>
                    <li>White-label solutions</li>
                    <li>Custom integration services</li>
                    <li>Enterprise dashboard and analytics</li>
                    <li>Compliance and audit support</li>
                    <li>Training and certification programs</li>
                </ul>
            </div>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number">$15M</span>
                <span class="stat-label">Annual B2B Revenue Potential</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">85%</span>
                <span class="stat-label">Gross Margin</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">500+</span>
                <span class="stat-label">Potential Enterprise Clients</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">24/7</span>
                <span class="stat-label">API Availability</span>
            </div>
        </div>
    </section>

    <!-- Financial Projections -->
    <section id="financial">
        <div class="section-header">
            <h2>Financial Projections</h2>
            <p>Revenue growth and cost analysis for scaling operations</p>
        </div>

        <div class="grid grid-2">
            <div class="card">
                <h3>Revenue Streams</h3>
                <div style="margin: 2rem 0;">
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Current B2C Revenue</span>
                        <span style="color: var(--primary); font-weight: 600;">$2.5M/year</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Enhanced Features Premium</span>
                        <span style="color: var(--accent); font-weight: 600;">+$3.5M/year</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Skip Tracing B2B</span>
                        <span style="color: var(--accent); font-weight: 600;">+$5M/year</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Government Contracts</span>
                        <span style="color: var(--accent); font-weight: 600;">+$15M/year</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; font-weight: bold; font-size: 1.2rem; border-top: 2px solid var(--primary);">
                        <span>Total Projected Revenue</span>
                        <span style="color: var(--primary);">$26M/year</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>Cost Analysis</h3>
                <div style="margin: 2rem 0;">
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Current Infrastructure</span>
                        <span style="color: var(--secondary); font-weight: 600;">$500K/year</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Google Cloud Migration Savings</span>
                        <span style="color: var(--accent); font-weight: 600;">-$250K/year</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Additional Development</span>
                        <span style="color: var(--secondary); font-weight: 600;">+$1M (one-time)</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid var(--dark-lighter);">
                        <span>Compliance & Legal</span>
                        <span style="color: var(--secondary); font-weight: 600;">+$300K/year</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 1rem 0; font-weight: bold; font-size: 1.2rem; border-top: 2px solid var(--accent);">
                        <span>Net ROI</span>
                        <span style="color: var(--accent);">+940%</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number">6</span>
                <span class="stat-label">Months to Break Even</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">10x</span>
                <span class="stat-label">Revenue Multiple</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">$100M</span>
                <span class="stat-label">5-Year Valuation</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">85%</span>
                <span class="stat-label">Gross Margin</span>
            </div>
        </div>
    </section>

    <!-- Implementation Timeline -->
    <section id="implementation">
        <div class="section-header">
            <h2>Implementation Timeline</h2>
            <p>Actionable roadmap for scaling FaceTrace operations</p>
        </div>

        <div class="timeline">
            <div class="timeline-item">
                <div class="card">
                    <h3>Q1 2025: Foundation</h3>
                    <ul class="feature-list">
                        <li>Migrate to Google Cloud Vision API</li>
                        <li>Implement advanced caching layer</li>
                        <li>Build API infrastructure for B2B</li>
                        <li>Hire compliance officer</li>
                        <li>Begin legal framework development</li>
                    </ul>
                </div>
            </div>

            <div class="timeline-item">
                <div class="card">
                    <h3>Q2 2025: Enhanced Features Launch</h3>
                    <ul class="feature-list">
                        <li>Deploy image enhancement technology</li>
                        <li>Integrate 50+ data sources</li>
                        <li>Launch premium tier pricing</li>
                        <li>Begin B2B pilot program</li>
                        <li>Establish partnerships with PI firms</li>
                    </ul>
                </div>
            </div>

            <div class="timeline-item">
                <div class="card">
                    <h3>Q3 2025: B2B Expansion</h3>
                    <ul class="feature-list">
                        <li>Full skip tracing API launch</li>
                        <li>Enterprise dashboard release</li>
                        <li>Major partner integrations</li>
                        <li>FCRA compliance certification</li>
                        <li>White-label solution development</li>
                    </ul>
                </div>
            </div>

            <div class="timeline-item">
                <div class="card">
                    <h3>Q4 2025: Government Integration</h3>
                    <ul class="feature-list">
                        <li>FedTrace pilot program launch</li>
                        <li>StateTrace rollout in 5 states</li>
                        <li>Security clearance process</li>
                        <li>FedRAMP certification</li>
                        <li>CountyTrace beta testing</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card" style="background: var(--gradient-1); color: white; text-align: center; margin-top: 4rem;">
            <h3 style="color: white; font-size: 2.5rem; margin-bottom: 2rem;">Ready to Scale?</h3>
            <p style="font-size: 1.3rem; margin: 2rem 0; line-height: 1.6;">
                FaceTrace is positioned to capture a massive market opportunity while maintaining privacy standards and compliance requirements.
            </p>
            <div class="stats-container" style="margin: 3rem 0;">
                <div class="stat-card" style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
                    <span class="stat-number" style="color: white;">75%</span>
                    <span class="stat-label" style="color: rgba(255,255,255,0.8);">Time Reduction</span>
                </div>
                <div class="stat-card" style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
                    <span class="stat-number" style="color: white;">96%</span>
                    <span class="stat-label" style="color: rgba(255,255,255,0.8);">Accuracy Rate</span>
                </div>
                <div class="stat-card" style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
                    <span class="stat-number" style="color: white;">24/7</span>
                    <span class="stat-label" style="color: rgba(255,255,255,0.8);">Availability</span>
                </div>
            </div>
            <div style="margin-top: 3rem;">
                <button class="btn" style="background: white; color: var(--dark); font-size: 1.2rem; padding: 1.5rem 3rem;">
                    Begin Implementation
                </button>
            </div>
        </div>
    </section>

    <script>
        // Global presentation state
        let currentSlideIndex = 0;
        let animationSequence = null;

        // Remove loader and start animations
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loader').classList.add('hidden');
                startCoverPageSequence();
            }, 500);
        });

        // Cover page animation sequence
        function startCoverPageSequence() {
            const steps = [
                { id: 'author-fade', delay: 2000 },
                { id: 'logo-variations', delay: 4000 },
                { id: 'surprise-section', delay: 7000 }
            ];

            let currentStep = 0;

            function showNextStep() {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    const element = document.getElementById(step.id);
                    
                    setTimeout(() => {
                        // Hide previous elements
                        if (currentStep > 0) {
                            const prevElement = document.getElementById(steps[currentStep - 1].id);
                            if (prevElement) {
                                prevElement.style.display = 'none';
                            }
                        }
                        
                        // Show current element
                        element.style.display = 'block';
                        setTimeout(() => {
                            element.classList.add('active');
                        }, 100);
                        
                        currentStep++;
                        showNextStep();
                    }, currentStep === 0 ? step.delay : 3000);
                }
            }

            showNextStep();
        }

        // Start main presentation
        function startPresentation() {
            // Fade out cover content
            document.querySelector('.cover-animation').style.opacity = '0';
            
            setTimeout(() => {
                // Scroll to executive summary
                document.getElementById('executive-summary').scrollIntoView({ behavior: 'smooth' });
                
                // Start puzzle animation
                setTimeout(() => {
                    animatePuzzlePieces();
                }, 1000);
            }, 500);
        }

        // Animate puzzle pieces
        function animatePuzzlePieces() {
            const puzzlePieces = document.querySelectorAll('.puzzle-piece');
            puzzlePieces.forEach((piece, index) => {
                const delay = parseInt(piece.dataset.delay) || index * 200;
                setTimeout(() => {
                    piece.classList.add('animate');
                }, delay);
            });
        }

        // Progress bar and navigation
        window.addEventListener('scroll', () => {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';

            // Update page counter
            const sections = document.querySelectorAll('section');
            let currentSection = 0;
            sections.forEach((section, index) => {
                const sectionTop = section.offsetTop;
                if (winScroll >= sectionTop - 200) {
                    currentSection = index + 1;
                }
            });
            document.getElementById('current-page').textContent = `Page ${currentSection}`;
        });

        // Navigation active state
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').slice(1) === current) {
                    link.classList.add('active');
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    
                    // Trigger specific animations
                    if (entry.target.id === 'data-puzzle') {
                        setTimeout(() => animatePuzzlePieces(), 500);
                    }
                }
            });
        }, observerOptions);

        sections.forEach(section => {
            observer.observe(section);
        });

        // Timeline items observer
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach(item => {
            observer.observe(item);
        });

        // Counter animation for stats
        const counters = document.querySelectorAll('.counter');
        const speed = 200;

        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                    entry.target.classList.add('counted');
                    const target = +entry.target.getAttribute('data-target');
                    const increment = target / speed;
                    
                    const updateCount = () => {
                        const count = +entry.target.innerText;
                        if (count < target) {
                            entry.target.innerText = Math.ceil(count + increment);
                            setTimeout(updateCount, 1);
                        } else {
                            entry.target.innerText = target;
                        }
                    };
                    
                    updateCount();
                }
            });
        }, { threshold: 0.5 });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });

        // Smooth scroll for navigation links
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').slice(1);
                const targetSection = document.getElementById(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                const currentSection = getCurrentSection();
                const nextSection = sections[currentSection + 1];
                if (nextSection) {
                    nextSection.scrollIntoView({ behavior: 'smooth' });
                }
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                const currentSection = getCurrentSection();
                const prevSection = sections[currentSection - 1];
                if (prevSection) {
                    prevSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });

        function getCurrentSection() {
            let current = 0;
            const scrollPos = window.pageYOffset;
            sections.forEach((section, index) => {
                if (scrollPos >= section.offsetTop - 200) {
                    current = index;
                }
            });
            return current;
        }

        // Enhanced hover effects for cards
        document.querySelectorAll('.card, .data-source-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Dynamic background gradient
        let gradientAngle = 0;
        setInterval(() => {
            gradientAngle += 0.5;
            document.querySelector('.bg-animation').style.background = 
                `linear-gradient(${gradientAngle}deg, var(--dark) 0%, rgba(26, 26, 26, 0.8) 50%, var(--dark) 100%)`;
        }, 100);
    </script>
</body>
</html>