<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scaling FaceTrace - It's <PERSON> Robbery</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #fff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        }

        .animated-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.1" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Progress Bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.1);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #ff0080);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Page Counter */
        .page-counter {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        /* Navigation */
        .nav-bar {
            position: fixed;
            top: 50%;
            right: 30px;
            transform: translateY(-50%);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .nav-bar.visible {
            opacity: 1;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: #00d4ff;
            transform: scale(1.5);
        }

        /* Slide Container */
        .slide-container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        /* Cover Page Styles */
        .cover-slide {
            text-align: center;
            padding: 2rem;
        }

        .logo-container {
            margin-bottom: 3rem;
            opacity: 0;
            animation: fadeInScale 1s ease 0.5s forwards;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .facetrace-logo {
            font-size: 4rem;
            font-weight: 900;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .face-count {
            font-size: 1.5rem;
            color: #888;
            margin-bottom: 2rem;
        }

        .author-info {
            opacity: 0;
            animation: fadeInUp 1s ease 1.5s forwards;
            margin-bottom: 2rem;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .gov-logos {
            opacity: 0;
            animation: fadeInUp 1s ease 2.5s forwards;
            margin-bottom: 3rem;
        }

        .gov-logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 1rem 0;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .sub-logos {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }

        .sub-logo {
            font-size: 1.2rem;
            color: #00d4ff;
            padding: 0.5rem 1rem;
            border: 1px solid #00d4ff;
            border-radius: 25px;
        }

        .surprise-btn {
            opacity: 0;
            animation: fadeInBounce 1s ease 3.5s forwards;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
        }

        @keyframes fadeInBounce {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.8);
            }
            60% {
                opacity: 1;
                transform: translateY(-10px) scale(1.1);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .surprise-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0, 212, 255, 0.5);
        }

        /* Loading Animation */
        .loading-slide {
            text-align: center;
        }

        .loading-text {
            font-size: 2rem;
            margin-bottom: 2rem;
            opacity: 0;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .countdown {
            font-size: 4rem;
            font-weight: 900;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Executive Summary Styles */
        .exec-slide {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .fade-text {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .fade-text.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .big-question {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .big-answer {
            font-size: 4rem;
            font-weight: 900;
            text-align: center;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 3rem;
        }

        .problem-statement {
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 2rem;
            color: #ff6b6b;
        }

        .vision-statement {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 2rem;
            font-weight: 700;
        }

        .solution-statement {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            color: #4ecdc4;
        }

        /* Puzzle Animation */
        .puzzle-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            max-width: 600px;
            margin: 2rem auto;
        }

        .puzzle-piece {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border: 2px solid #00d4ff;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            opacity: 0;
            transform: scale(0.8) rotate(10deg);
            transition: all 0.8s ease;
        }

        .puzzle-piece.animate {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        .puzzle-piece h4 {
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }

        .puzzle-piece p {
            font-size: 0.9rem;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    
    <!-- Progress Bar -->
    <div class="progress-bar">
        <div class="progress-fill"></div>
    </div>
    
    <!-- Page Counter -->
    <div class="page-counter">
        <span id="current-page">1</span> / <span id="total-pages">5</span>
    </div>
    
    <!-- Navigation Dots -->
    <div class="nav-bar" id="nav-bar">
        <div class="nav-dot active" data-slide="0"></div>
        <div class="nav-dot" data-slide="1"></div>
        <div class="nav-dot" data-slide="2"></div>
        <div class="nav-dot" data-slide="3"></div>
        <div class="nav-dot" data-slide="4"></div>
    </div>

    <!-- Slide Container -->
    <div class="slide-container">
        <!-- Slide 1: Cover Page -->
        <div class="slide active cover-slide">
            <div class="logo-container">
                <div class="facetrace-logo" id="main-logo">FaceTrace</div>
                <div class="face-count" id="face-count">2.4 Billion Faces Indexed</div>
            </div>

            <div class="author-info" id="author-info">
                <div style="font-size: 1.3rem; margin-bottom: 0.5rem;">Bryce Bayens</div>
                <div style="font-size: 1.1rem; color: #888;">June 2025 | FaceTrace.pro</div>
            </div>

            <div class="gov-logos" id="gov-logos" style="display: none;">
                <div class="gov-logo">FaceTraceGov</div>
                <div class="sub-logos">
                    <div class="sub-logo">FedTrace</div>
                    <div class="sub-logo">StateTrace</div>
                    <div class="sub-logo">CountyTrace</div>
                </div>
                <div style="font-size: 1.5rem; color: #ff6b6b; margin-top: 1rem;">Suspect Identification</div>
            </div>

            <button class="surprise-btn" id="surprise-btn" onclick="startPresentation()">
                Surprise Me
            </button>
        </div>

        <!-- Slide 2: Loading -->
        <div class="slide loading-slide">
            <div class="loading-text">One Moment...</div>
            <div class="countdown" id="countdown">5</div>
        </div>

        <!-- Slide 3: Executive Summary -->
        <div class="slide exec-slide">
            <div class="fade-text" id="question1">
                <div class="big-question">What is the most valuable asset in the world?</div>
            </div>
            <div class="fade-text" id="answer1" style="display: none;">
                <div class="big-answer">DATA</div>
            </div>
            <div class="fade-text" id="problem" style="display: none;">
                <div class="problem-statement">The Problem: Agencies crowdsource information to identify suspects.</div>
                <div style="text-align: center; font-size: 1.5rem; color: #ff0080; margin-top: 1rem;">⏰ Time is ticking</div>
            </div>
            <div class="fade-text" id="vision" style="display: none;">
                <div class="vision-statement">The Vision: Make America Safe Again</div>
            </div>
            <div class="fade-text" id="solution" style="display: none;">
                <div class="solution-statement">The Solution: Data is a puzzle. Piece the puzzle.</div>
            </div>
            <div class="fade-text" id="facetrace-gov" style="display: none;">
                <div style="font-size: 3rem; text-align: center; margin-bottom: 1rem; background: linear-gradient(135deg, #ff6b6b, #4ecdc4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">FaceTraceGov</div>
                <div style="font-size: 2rem; text-align: center; color: #ff0080; font-weight: 700;">It's Highway Robbery</div>
            </div>
        </div>

        <!-- Slide 4: Data Puzzle -->
        <div class="slide exec-slide">
            <div style="text-align: center; margin-bottom: 3rem;">
                <h2 style="font-size: 2.5rem; background: linear-gradient(135deg, #00d4ff, #ff0080); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">The FaceTraceGov Data Puzzle</h2>
                <p style="font-size: 1.2rem; color: #ccc; margin-top: 1rem;">Piecing together data sources for comprehensive suspect identification</p>
            </div>

            <div class="puzzle-container" id="puzzle-container">
                <div class="puzzle-piece" data-delay="0">
                    <h4>TLO (TransUnion)</h4>
                    <p>96% accuracy • $1.80/search</p>
                    <p>Advanced skip tracing algorithms</p>
                </div>
                <div class="puzzle-piece" data-delay="200">
                    <h4>IDI Core</h4>
                    <p>88% accuracy • $0.50/search</p>
                    <p>Profiles on every U.S. adult</p>
                </div>
                <div class="puzzle-piece" data-delay="400">
                    <h4>Tracers</h4>
                    <p>98% U.S. coverage</p>
                    <p>2,000+ law enforcement users</p>
                </div>
                <div class="puzzle-piece" data-delay="600">
                    <h4>Clear (Thomson Reuters)</h4>
                    <p>Professional-grade platform</p>
                    <p>Comprehensive public records</p>
                </div>
                <div class="puzzle-piece" data-delay="800">
                    <h4>Accurint (LexisNexis)</h4>
                    <p>Extensive database</p>
                    <p>Enterprise-level security</p>
                </div>
                <div class="puzzle-piece" data-delay="1000">
                    <h4>Face Recognition AI</h4>
                    <p>Real-time processing</p>
                    <p>Enhanced image quality</p>
                </div>
            </div>
        </div>

        <!-- Slide 5: Architecture Overview -->
        <div class="slide exec-slide">
            <div style="text-align: center; margin-bottom: 3rem;">
                <h2 style="font-size: 2.5rem; background: linear-gradient(135deg, #00d4ff, #ff0080); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Advanced Architecture</h2>
                <p style="font-size: 1.2rem; color: #ccc; margin-top: 1rem;">Next-generation facial recognition with enhanced image processing</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 2px solid #00d4ff; border-radius: 15px; padding: 2rem;">
                    <h3 style="color: #00d4ff; margin-bottom: 1rem;">Image Enhancement</h3>
                    <ul style="list-style: none; color: #ccc;">
                        <li>✓ Grainy security camera optimization</li>
                        <li>✓ Low-light enhancement algorithms</li>
                        <li>✓ Motion blur correction</li>
                        <li>✓ Super-resolution upscaling</li>
                    </ul>
                </div>

                <div style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 2px solid #ff6b6b; border-radius: 15px; padding: 2rem;">
                    <h3 style="color: #ff6b6b; margin-bottom: 1rem;">AI Processing</h3>
                    <ul style="list-style: none; color: #ccc;">
                        <li>✓ Advanced CNN models</li>
                        <li>✓ Real-time face detection</li>
                        <li>✓ Multi-angle recognition</li>
                        <li>✓ Bias mitigation algorithms</li>
                    </ul>
                </div>

                <div style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 2px solid #4ecdc4; border-radius: 15px; padding: 2rem;">
                    <h3 style="color: #4ecdc4; margin-bottom: 1rem;">Data Integration</h3>
                    <ul style="list-style: none; color: #ccc;">
                        <li>✓ Skip tracing database fusion</li>
                        <li>✓ Criminal record cross-reference</li>
                        <li>✓ Social media profile linking</li>
                        <li>✓ Real-time identity verification</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = 5;
        let isAnimating = false;

        // Update page counter and progress
        function updateProgress() {
            document.getElementById('current-page').textContent = currentSlide + 1;
            document.getElementById('total-pages').textContent = totalSlides;

            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.querySelector('.progress-fill').style.width = progress + '%';

            // Update navigation dots
            document.querySelectorAll('.nav-dot').forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
        }

        // Show navigation after first slide
        function showNavigation() {
            document.getElementById('nav-bar').classList.add('visible');
        }

        // Start presentation sequence
        function startPresentation() {
            if (isAnimating) return;
            isAnimating = true;

            // Fade out author info
            const authorInfo = document.getElementById('author-info');
            authorInfo.style.opacity = '0';

            setTimeout(() => {
                // Show government logos
                document.getElementById('gov-logos').style.display = 'block';
                document.getElementById('gov-logos').style.opacity = '1';

                setTimeout(() => {
                    // Hide surprise button and start countdown
                    document.getElementById('surprise-btn').style.display = 'none';
                    nextSlide();
                    startCountdown();
                }, 3000);
            }, 1000);
        }

        // Countdown animation
        function startCountdown() {
            let count = 5;
            const countdownEl = document.getElementById('countdown');

            const countInterval = setInterval(() => {
                count--;
                countdownEl.textContent = count;

                if (count <= 0) {
                    clearInterval(countInterval);
                    setTimeout(() => {
                        nextSlide();
                        showNavigation();
                        startExecutiveSummary();
                    }, 500);
                }
            }, 1000);
        }

        // Executive summary animation sequence
        function startExecutiveSummary() {
            const elements = ['question1', 'answer1', 'problem', 'vision', 'solution', 'facetrace-gov'];
            let index = 0;

            function showNext() {
                if (index > 0) {
                    document.getElementById(elements[index - 1]).style.display = 'none';
                }

                if (index < elements.length) {
                    const el = document.getElementById(elements[index]);
                    el.style.display = 'block';
                    setTimeout(() => {
                        el.classList.add('visible');
                    }, 100);

                    setTimeout(() => {
                        index++;
                        showNext();
                    }, 2500);
                }
            }

            showNext();
        }

        // Navigate to specific slide
        function goToSlide(slideIndex) {
            if (isAnimating || slideIndex === currentSlide) return;

            const slides = document.querySelectorAll('.slide');
            slides[currentSlide].classList.remove('active');

            currentSlide = slideIndex;
            slides[currentSlide].classList.add('active');

            updateProgress();

            // Trigger puzzle animation if on puzzle slide
            if (currentSlide === 3) {
                animatePuzzle();
            }
        }

        // Next slide
        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                goToSlide(currentSlide + 1);
            }
        }

        // Animate puzzle pieces
        function animatePuzzle() {
            const pieces = document.querySelectorAll('.puzzle-piece');
            pieces.forEach((piece, index) => {
                const delay = parseInt(piece.dataset.delay);
                setTimeout(() => {
                    piece.classList.add('animate');
                }, delay);
            });
        }

        // Navigation dot click handlers
        document.querySelectorAll('.nav-dot').forEach((dot, index) => {
            dot.addEventListener('click', () => goToSlide(index));
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                if (currentSlide > 0) {
                    goToSlide(currentSlide - 1);
                }
            }
        });

        // Initialize
        updateProgress();
    </script>
</body>
</html>
