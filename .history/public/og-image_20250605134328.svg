<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630" fill="none">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0" y1="0" x2="1200" y2="630" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#0F172A" /> <!-- Dark background -->
      <stop offset="50%" stop-color="#1E293B" /> <!-- Slate-800 -->
      <stop offset="100%" stop-color="#0F172A" /> <!-- Dark background -->
    </linearGradient>
    <linearGradient id="primary-gradient" x1="0" y1="0" x2="100%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#5158f6" /> <!-- Primary -->
      <stop offset="50%" stop-color="#7C81FF" /> <!-- Primary light -->
      <stop offset="100%" stop-color="#3D40CC" /> <!-- Primary dark -->
    </linearGradient>
    <linearGradient id="text-gradient" x1="0" y1="0" x2="100%" y2="0" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#FFFFFF" />
      <stop offset="50%" stop-color="#E2E8F0" />
      <stop offset="100%" stop-color="#CBD5E1" />
    </linearGradient>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    <filter id="logo-glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="12" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>

  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)" />

  <!-- Modern grid pattern -->
  <defs>
    <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#5158f6" stroke-width="0.5" stroke-opacity="0.1"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)" />

  <!-- Floating geometric elements -->
  <circle cx="100" cy="100" r="2" fill="#5158f6" fill-opacity="0.3" />
  <circle cx="1100" cy="150" r="3" fill="#7C81FF" fill-opacity="0.4" />
  <circle cx="150" cy="500" r="1.5" fill="#3D40CC" fill-opacity="0.5" />
  <circle cx="1050" cy="480" r="2.5" fill="#5158f6" fill-opacity="0.3" />
  
  <!-- Left side: Logo -->
  <g transform="translate(200, 245)">
    <!-- Logo wrapper with subtle glow -->
    <g filter="url(#glow)">
      <!-- Face outline -->
      <polygon 
        points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25" 
        stroke="white" 
        stroke-width="4" 
        fill="white"
        fill-opacity="0.1"
        stroke-linejoin="round"
      />
      
      <!-- Biometric grid lines -->
      <path d="M50,10 L50,90" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      <path d="M15,50 L85,50" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      <path d="M25,25 L75,75" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      <path d="M25,75 L75,25" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      
      <!-- Eyes -->
      <rect x="32" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" fill="none" />
      <rect x="60" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" fill="none" />
      
      <!-- Mouth -->
      <path d="M35,65 L65,65" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" />
    </g>
  </g>
  
  <!-- Right side: Features with modern icons -->
  <!-- Feature 1: Find Stolen Images -->
  <g transform="translate(450, 180)">
    <circle cx="25" cy="25" r="25" fill="white" fill-opacity="0.15" />
    <path d="M15,25 L22,32 L35,18" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
    <text x="70" y="32" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" font-weight="500">Find Stolen Images</text>
  </g>
  
  <!-- Feature 2: Verify Online Identities -->
  <g transform="translate(450, 280)">
    <circle cx="25" cy="25" r="25" fill="white" fill-opacity="0.15" />
    <path d="M15,25 L22,32 L35,18" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
    <text x="70" y="32" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" font-weight="500">Verify Online Identities</text>
  </g>
  
  <!-- Feature 3: Protect Your Privacy -->
  <g transform="translate(450, 380)">
    <circle cx="25" cy="25" r="25" fill="white" fill-opacity="0.15" />
    <path d="M15,25 L22,32 L35,18" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
    <text x="70" y="32" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" font-weight="500">Protect Your Privacy</text>
  </g>
  
  <!-- Brand text with modern typography -->
  <g transform="translate(600, 470)">
    <text x="0" y="0" font-family="'Electrolize', system-ui, -apple-system, sans-serif" font-size="72" font-weight="700" fill="white" text-anchor="middle">FaceTrace Pro</text>
  </g>
  
  <!-- URL at the bottom -->
  <text x="600" y="550" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" fill-opacity="0.9" text-anchor="middle">https://facetrace.pro</text>
</svg> 