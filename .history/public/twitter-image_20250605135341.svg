<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630" fill="none">
  <!-- Enhanced background with modern gradients -->
  <defs>
    <!-- Main background gradient -->
    <radialGradient id="bg-gradient" cx="50%" cy="50%" r="80%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#1E293B" />
      <stop offset="60%" stop-color="#0F172A" />
      <stop offset="100%" stop-color="#020617" />
    </radialGradient>

    <!-- Brand gradient -->
    <linearGradient id="brand-gradient" x1="0%" y1="0%" x2="100%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#5158f6" />
      <stop offset="50%" stop-color="#7C81FF" />
      <stop offset="100%" stop-color="#3D40CC" />
    </linearGradient>

    <!-- Text gradient using site foreground -->
    <linearGradient id="text-gradient" x1="0%" y1="0%" x2="100%" y2="0%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#F8FAFC" /> <!-- Site foreground -->
      <stop offset="50%" stop-color="#FFFFFF" />
      <stop offset="100%" stop-color="#F8FAFC" /> <!-- Site foreground -->
    </linearGradient>

    <!-- Glow effects -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="12" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>

    <!-- Grid pattern using site border color -->
    <pattern id="tech-grid" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#334155" stroke-width="0.6" stroke-opacity="0.3"/>
      <circle cx="0" cy="0" r="0.8" fill="#5158f6" fill-opacity="0.2" />
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)" />

  <!-- Tech grid overlay -->
  <rect width="1200" height="630" fill="url(#tech-grid)" />

  <!-- Floating particles for depth -->
  <circle cx="150" cy="100" r="2.5" fill="#5158f6" fill-opacity="0.4">
    <animate attributeName="cy" values="100;120;100" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1050" cy="150" r="2" fill="#7C81FF" fill-opacity="0.5">
    <animate attributeName="cy" values="150;170;150" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="500" r="1.8" fill="#3D40CC" fill-opacity="0.6">
    <animate attributeName="cy" values="500;480;500" dur="5s" repeatCount="indefinite"/>
  </circle>

  <!-- Logo centered in the image with enhanced design -->
  <g transform="translate(600, 200)">
    <!-- Logo background aura -->
    <circle cx="0" cy="0" r="100" fill="url(#brand-gradient)" fill-opacity="0.1" filter="url(#glow)" />

    <!-- Main logo -->
    <g filter="url(#glow)">
      <!-- Face outline - enhanced geometric design -->
      <polygon
        points="0,-60 -35,-35 -50,0 -35,35 0,60 35,35 50,0 35,-35"
        stroke="white"
        stroke-width="5"
        fill="url(#brand-gradient)"
        fill-opacity="0.2"
        stroke-linejoin="round"
      />

      <!-- Advanced biometric grid -->
      <path d="M0,-60 L0,60" stroke="#7C81FF" stroke-width="1.5" stroke-opacity="0.8" stroke-dasharray="5 3" />
      <path d="M-50,0 L50,0" stroke="#7C81FF" stroke-width="1.5" stroke-opacity="0.8" stroke-dasharray="5 3" />
      <path d="M-35,-35 L35,35" stroke="#5158f6" stroke-width="1.2" stroke-opacity="0.7" stroke-dasharray="3 2" />
      <path d="M-35,35 L35,-35" stroke="#5158f6" stroke-width="1.2" stroke-opacity="0.7" stroke-dasharray="3 2" />

      <!-- Modern eyes with tech styling -->
      <rect x="-25" y="-15" width="12" height="10" rx="3" stroke="#7C81FF" stroke-width="3" fill="url(#brand-gradient)" fill-opacity="0.3" />
      <rect x="13" y="-15" width="12" height="10" rx="3" stroke="#7C81FF" stroke-width="3" fill="url(#brand-gradient)" fill-opacity="0.3" />

      <!-- Mouth - straight line for serious expression -->
      <path d="M-20,20 L20,20" stroke="#7C81FF" stroke-width="2.5" fill="none" stroke-linecap="round" />

      <!-- Biometric measurement points with animation -->
      <circle cx="0" cy="-30" r="2" fill="#5158f6">
        <animate attributeName="r" values="2;3;2" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="30" r="2" fill="#5158f6">
        <animate attributeName="r" values="2;3;2" dur="2s" begin="0.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-25" cy="0" r="2" fill="#5158f6">
        <animate attributeName="r" values="2;3;2" dur="2s" begin="1s" repeatCount="indefinite"/>
      </circle>
      <circle cx="25" cy="0" r="2" fill="#5158f6">
        <animate attributeName="r" values="2;3;2" dur="2s" begin="1.5s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>

  <!-- Brand text with enhanced typography -->
  <g transform="translate(600, 400)">
    <!-- Main title -->
    <text x="0" y="0" font-family="'Electrolize', 'SF Mono', monospace" font-size="72" font-weight="700" fill="url(#text-gradient)" text-anchor="middle" filter="url(#glow)">FaceTrace</text>

    <!-- Animated separator dot -->
    <circle cx="160" cy="-15" r="4" fill="#5158f6">
      <animate attributeName="opacity" values="1;0.4;1" dur="2s" repeatCount="indefinite"/>
    </circle>

    <!-- Pro text -->
    <text x="0" y="60" font-family="'Electrolize', 'SF Mono', monospace" font-size="42" font-weight="600" fill="url(#brand-gradient)" text-anchor="middle">Pro</text>
  </g>

  <!-- Enhanced tagline -->
  <text x="600" y="520" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" fill-opacity="0.9" text-anchor="middle" font-weight="300">Find Where Faces Appear Across The Web</text>

  <!-- Website URL -->
  <text x="600" y="580" font-family="'Electrolize', 'SF Mono', monospace" font-size="24" fill="#7C81FF" fill-opacity="0.8" text-anchor="middle" font-weight="500">facetrace.pro</text>
</svg>