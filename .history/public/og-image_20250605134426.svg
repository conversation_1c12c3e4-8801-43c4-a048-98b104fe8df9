<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630" fill="none">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0" y1="0" x2="1200" y2="630" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#0F172A" /> <!-- Dark background -->
      <stop offset="50%" stop-color="#1E293B" /> <!-- Slate-800 -->
      <stop offset="100%" stop-color="#0F172A" /> <!-- Dark background -->
    </linearGradient>
    <linearGradient id="primary-gradient" x1="0" y1="0" x2="100%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#5158f6" /> <!-- Primary -->
      <stop offset="50%" stop-color="#7C81FF" /> <!-- Primary light -->
      <stop offset="100%" stop-color="#3D40CC" /> <!-- Primary dark -->
    </linearGradient>
    <linearGradient id="text-gradient" x1="0" y1="0" x2="100%" y2="0" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#FFFFFF" />
      <stop offset="50%" stop-color="#E2E8F0" />
      <stop offset="100%" stop-color="#CBD5E1" />
    </linearGradient>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    <filter id="logo-glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="12" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>

  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)" />

  <!-- Modern grid pattern -->
  <defs>
    <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#5158f6" stroke-width="0.5" stroke-opacity="0.1"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)" />

  <!-- Floating geometric elements -->
  <circle cx="100" cy="100" r="2" fill="#5158f6" fill-opacity="0.3" />
  <circle cx="1100" cy="150" r="3" fill="#7C81FF" fill-opacity="0.4" />
  <circle cx="150" cy="500" r="1.5" fill="#3D40CC" fill-opacity="0.5" />
  <circle cx="1050" cy="480" r="2.5" fill="#5158f6" fill-opacity="0.3" />

  <!-- Centered Logo with enhanced design -->
  <g transform="translate(150, 200)">
    <!-- Logo background glow -->
    <circle cx="75" cy="75" r="85" fill="url(#primary-gradient)" fill-opacity="0.1" filter="url(#logo-glow)" />

    <!-- Logo wrapper with enhanced glow -->
    <g filter="url(#glow)">
      <!-- Face outline - matching site design -->
      <polygon
        points="75,25 50,40 40,75 50,110 75,125 100,110 110,75 100,40"
        stroke="white"
        stroke-width="5"
        fill="url(#primary-gradient)"
        fill-opacity="0.15"
        stroke-linejoin="round"
      />

      <!-- Enhanced biometric grid lines -->
      <path d="M75,25 L75,125" stroke="#7C81FF" stroke-width="1.2" stroke-opacity="0.7" stroke-dasharray="4 3" />
      <path d="M40,75 L110,75" stroke="#7C81FF" stroke-width="1.2" stroke-opacity="0.7" stroke-dasharray="4 3" />
      <path d="M50,40 L100,110" stroke="#5158f6" stroke-width="1" stroke-opacity="0.6" stroke-dasharray="3 2" />
      <path d="M50,110 L100,40" stroke="#5158f6" stroke-width="1" stroke-opacity="0.6" stroke-dasharray="3 2" />

      <!-- Modern eyes with glow -->
      <rect x="60" y="65" width="10" height="8" rx="2" stroke="#7C81FF" stroke-width="3" fill="none" />
      <rect x="80" y="65" width="10" height="8" rx="2" stroke="#7C81FF" stroke-width="3" fill="none" />

      <!-- Enhanced mouth -->
      <path d="M60,95 Q75,105 90,95" stroke="#7C81FF" stroke-width="2.5" fill="none" stroke-linecap="round" />

      <!-- Biometric measurement points -->
      <circle cx="75" cy="60" r="1.5" fill="#5158f6" />
      <circle cx="75" cy="90" r="1.5" fill="#5158f6" />
      <circle cx="55" cy="75" r="1.5" fill="#5158f6" />
      <circle cx="95" cy="75" r="1.5" fill="#5158f6" />
    </g>
  </g>

  <!-- Main brand text with enhanced typography -->
  <g transform="translate(600, 200)">
    <!-- Main title with gradient -->
    <text x="0" y="0" font-family="'Electrolize', monospace" font-size="84" font-weight="700" fill="url(#text-gradient)" text-anchor="middle" filter="url(#glow)">FaceTrace</text>
    <!-- Animated dot -->
    <circle cx="180" cy="-15" r="4" fill="#5158f6">
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <!-- Pro text -->
    <text x="0" y="70" font-family="'Electrolize', monospace" font-size="48" font-weight="600" fill="url(#primary-gradient)" text-anchor="middle">Pro</text>
  </g>

  <!-- Tagline -->
  <text x="600" y="320" font-family="system-ui, -apple-system, sans-serif" font-size="32" fill="white" fill-opacity="0.9" text-anchor="middle" font-weight="300">Find Where Faces Appear Across The Web</text>

  <!-- Right side: Enhanced features with modern icons -->
  <!-- Feature 1: AI-Powered Search -->
  <g transform="translate(450, 380)">
    <circle cx="30" cy="30" r="30" fill="url(#primary-gradient)" fill-opacity="0.2" stroke="#5158f6" stroke-width="2" stroke-opacity="0.5" />
    <!-- AI Brain icon -->
    <path d="M20,25 Q25,15 35,20 Q40,25 35,35 Q30,40 20,35 Q15,30 20,25" stroke="#7C81FF" stroke-width="2.5" fill="none" stroke-linecap="round" />
    <circle cx="25" cy="25" r="2" fill="#5158f6" />
    <circle cx="35" cy="30" r="1.5" fill="#7C81FF" />
    <text x="80" y="38" font-family="'Electrolize', system-ui, sans-serif" font-size="24" fill="white" font-weight="500">AI-Powered Search</text>
  </g>

  <!-- Feature 2: Real-time Results -->
  <g transform="translate(450, 450)">
    <circle cx="30" cy="30" r="30" fill="url(#primary-gradient)" fill-opacity="0.2" stroke="#5158f6" stroke-width="2" stroke-opacity="0.5" />
    <!-- Lightning bolt icon -->
    <path d="M25,15 L20,30 L30,25 L25,45 L35,30 L25,35 Z" stroke="#7C81FF" stroke-width="2.5" fill="#5158f6" fill-opacity="0.3" stroke-linejoin="round" />
    <text x="80" y="38" font-family="'Electrolize', system-ui, sans-serif" font-size="24" fill="white" font-weight="500">Real-time Results</text>
  </g>

  <!-- Feature 3: Privacy Protected -->
  <g transform="translate(450, 520)">
    <circle cx="30" cy="30" r="30" fill="url(#primary-gradient)" fill-opacity="0.2" stroke="#5158f6" stroke-width="2" stroke-opacity="0.5" />
    <!-- Shield icon -->
    <path d="M30,15 L20,20 L20,35 Q20,45 30,50 Q40,45 40,35 L40,20 Z" stroke="#7C81FF" stroke-width="2.5" fill="none" stroke-linejoin="round" />
    <path d="M25,30 L28,33 L35,25" stroke="#5158f6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    <text x="80" y="38" font-family="'Electrolize', system-ui, sans-serif" font-size="24" fill="white" font-weight="500">Privacy Protected</text>
  </g>

  <!-- URL at the bottom with enhanced styling -->
  <text x="600" y="590" font-family="'Electrolize', system-ui, sans-serif" font-size="24" fill="#7C81FF" fill-opacity="0.8" text-anchor="middle" font-weight="500">facetrace.pro</text>
</svg>