<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630" fill="none">
  <!-- Enhanced background with modern gradients -->
  <defs>
    <!-- Main background gradient -->
    <radialGradient id="bg-gradient" cx="50%" cy="50%" r="80%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#1E293B" />
      <stop offset="60%" stop-color="#0F172A" />
      <stop offset="100%" stop-color="#020617" />
    </radialGradient>

    <!-- Brand gradient -->
    <linearGradient id="brand-gradient" x1="0%" y1="0%" x2="100%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#5158f6" />
      <stop offset="50%" stop-color="#7C81FF" />
      <stop offset="100%" stop-color="#3D40CC" />
    </linearGradient>

    <!-- Text gradient -->
    <linearGradient id="text-gradient" x1="0%" y1="0%" x2="100%" y2="0%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#FFFFFF" />
      <stop offset="50%" stop-color="#F1F5F9" />
      <stop offset="100%" stop-color="#E2E8F0" />
    </linearGradient>

    <!-- Glow effects -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="12" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>

    <!-- Tech grid pattern -->
    <pattern id="tech-grid" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#5158f6" stroke-width="0.6" stroke-opacity="0.12"/>
      <circle cx="0" cy="0" r="0.8" fill="#7C81FF" fill-opacity="0.25" />
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)" />

  <!-- Tech grid overlay -->
  <rect width="1200" height="630" fill="url(#tech-grid)" />
  
  <!-- Logo centered in the image -->
  <g transform="translate(360, 165) scale(1.5)">
    <!-- Face outline - geometric with bolder stroke -->
    <polygon 
      points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25" 
      stroke="white" 
      stroke-width="4" 
      stroke-opacity="1" 
      fill="none" 
      stroke-linejoin="round"
    />
    
    <!-- Biometric identification grid -->
    <path d="M50,10 L50,90" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    <path d="M15,50 L85,50" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    <path d="M25,25 L75,75" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    <path d="M25,75 L75,25" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    
    <!-- Eyes - geometric with thicker stroke -->
    <rect x="32" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" stroke-opacity="1" fill="none" />
    <rect x="60" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" stroke-opacity="1" fill="none" />
    
    <!-- Mouth - straight line for serious expression -->
    <path d="M35,65 L65,65" stroke="white" stroke-width="2" stroke-opacity="0.9" fill="none" stroke-linecap="round" />
    
    <!-- Intersection points with larger dots -->
    <circle cx="50" cy="10" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="25" cy="25" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="15" cy="50" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="25" cy="75" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="50" cy="90" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="75" cy="75" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="85" cy="50" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="75" cy="25" r="1.5" fill="white" fill-opacity="1" />
    
    <!-- Biometric measurement points -->
    <circle cx="50" cy="45" r="1" fill="white" fill-opacity="0.7" />
    <circle cx="50" cy="65" r="1" fill="white" fill-opacity="0.7" />
    <circle cx="35" cy="50" r="1" fill="white" fill-opacity="0.7" />
    <circle cx="65" cy="50" r="1" fill="white" fill-opacity="0.7" />
  </g>
  
  <!-- Brand text -->
  <text x="600" y="450" font-family="'Electrolize', monospace" font-size="64" font-weight="bold" fill="url(#text-gradient)" text-anchor="middle">FaceTrace Pro</text>
  <text x="600" y="500" font-family="sans-serif" font-size="24" fill="white" fill-opacity="0.8" text-anchor="middle">Find Where Faces Appear Across The Web</text>
</svg> 