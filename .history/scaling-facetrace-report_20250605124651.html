<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scaling FaceTrace - It's <PERSON> Robbery</title>
    <meta name="description" content="Piecing together the data puzzle for government agencies">
    <meta name="author" content="Bryce Bayens">
    <meta name="date" content="December 2024">
    
    <style>
        :root {
            --primary: #00d4ff;
            --secondary: #ff6b6b;
            --accent: #4ecdc4;
            --purple: #9b59b6;
            --orange: #f39c12;
            --green: #27ae60;
            --dark: #0a0a0a;
            --dark-light: #1a1a1a;
            --dark-lighter: #2a2a2a;
            --text: #ffffff;
            --text-dim: #a0a0a0;
            --gradient-1: linear-gradient(135deg, #00d4ff, #ff6b6b);
            --gradient-2: linear-gradient(135deg, #4ecdc4, #556270);
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            --shadow-lg: 0 20px 50px rgba(0, 0, 0, 0.7);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--dark);
            color: var(--text);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--dark);
            overflow: hidden;
        }

        .bg-animation::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--primary) 0%, transparent 70%);
            opacity: 0.05;
            animation: rotate 30s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Modern Navigation */
        nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 1rem 2rem;
            transition: all 0.3s ease;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            font-size: 1.5rem;
            font-weight: 900;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-link {
            color: var(--text-dim);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 25px;
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--primary);
            background: rgba(0, 212, 255, 0.1);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 80%;
        }

        /* Progress Bar */
        .progress-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: var(--gradient-1);
            z-index: 1001;
            transition: width 0.3s ease;
        }

        /* Hero Animation Sequence */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            margin-top: 80px; /* Account for fixed nav */
        }

        .hero-sequence {
            text-align: center;
            max-width: 1200px;
            position: relative;
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-text {
            position: absolute;
            opacity: 0;
            font-size: clamp(2rem, 6vw, 4rem);
            font-weight: 900;
            width: 100%;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .hero-text.active {
            opacity: 1;
            transform: translateY(0);
        }

        .hero-text.fade-out {
            opacity: 0;
            transform: translateY(-30px);
        }

        .hero-text .highlight {
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-top: 1rem;
        }

        .hero-text .subtitle {
            display: block;
            font-size: 0.4em;
            font-weight: 300;
            color: var(--text-dim);
            margin-top: 1rem;
        }

        /* Presentation Title */
        .presentation-title {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(10, 10, 10, 0.9);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .presentation-title h1 {
            font-size: 1.2rem;
            margin: 0 0 0.5rem 0;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .presentation-title p {
            font-size: 0.9rem;
            color: var(--text-dim);
            margin: 0;
        }

        /* Animated Puzzle Visualization */
        .puzzle-visualization {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            width: 250px;
            height: 250px;
            z-index: 100;
            transition: all 0.3s ease;
        }

        .puzzle-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 10, 0.8);
            border-radius: 15px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            padding: 20px;
        }

        .puzzle-piece {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 8px;
            opacity: 0;
            transform: scale(0.5) rotate(45deg);
            transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .puzzle-piece.revealed {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        .puzzle-piece:nth-child(1) { top: 20px; left: 20px; background: var(--primary); }
        .puzzle-piece:nth-child(2) { top: 20px; right: 20px; background: var(--secondary); }
        .puzzle-piece:nth-child(3) { bottom: 20px; left: 20px; background: var(--accent); }
        .puzzle-piece:nth-child(4) { bottom: 20px; right: 20px; background: var(--purple); }
        .puzzle-piece:nth-child(5) { top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0.5) rotate(45deg); background: var(--orange); }

        .puzzle-piece.revealed:nth-child(5) {
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
        }

        .puzzle-label {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            color: var(--text-dim);
            text-align: center;
            white-space: nowrap;
        }

        /* Sections */
        section {
            padding: 5rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: clamp(2rem, 5vw, 3rem);
            margin-bottom: 1rem;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-header p {
            font-size: 1.2rem;
            color: var(--text-dim);
            max-width: 800px;
            margin: 0 auto;
        }

        /* Puzzle Cards */
        .puzzle-card {
            background: var(--dark-light);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .puzzle-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent), var(--purple));
            border-radius: 20px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .puzzle-card:hover::before {
            opacity: 1;
        }

        .puzzle-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .puzzle-card h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        /* Grid Layouts */
        .grid {
            display: grid;
            gap: 2rem;
            margin-top: 3rem;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        /* Data Source Puzzle Pieces */
        .data-sources {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 3rem 0;
        }

        .data-source-piece {
            background: var(--dark-lighter);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .data-source-piece::after {
            content: '🧩';
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 1.5rem;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .data-source-piece:hover {
            transform: scale(1.05) rotate(1deg);
            border-color: var(--primary);
        }

        .data-source-piece:hover::after {
            opacity: 1;
            transform: rotate(15deg);
        }

        .data-source-piece h4 {
            color: var(--accent);
            margin-bottom: 0.5rem;
        }

        /* Architecture Diagram */
        .architecture-puzzle {
            background: var(--dark-lighter);
            border-radius: 20px;
            padding: 3rem;
            margin: 3rem 0;
            position: relative;
            min-height: 400px;
        }

        /* Stats */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .stat-card {
            background: var(--dark-lighter);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            border: 2px solid var(--dark-lighter);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(0, 212, 255, 0.1));
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .stat-card:hover::before {
            transform: translateX(0);
        }

        .stat-card:hover {
            transform: scale(1.05);
            border-color: var(--primary);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }

        .stat-label {
            color: var(--text-dim);
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        /* Code Blocks */
        .code-block {
            background: #000;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            overflow-x: auto;
            border: 1px solid var(--dark-lighter);
        }

        .code-block pre {
            color: #f8f8f2;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .code-block .keyword {
            color: #ff79c6;
        }

        .code-block .string {
            color: #f1fa8c;
        }

        .code-block .comment {
            color: #6272a4;
        }

        .code-block .function {
            color: #50fa7b;
        }

        /* Feature List */
        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 1rem 0;
            border-bottom: 1px solid var(--dark-lighter);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .feature-list li:hover {
            padding-left: 1rem;
            color: var(--primary);
        }

        .feature-list li::before {
            content: '🧩';
            font-size: 1.2rem;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--gradient-1);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            nav {
                top: 10px;
                max-width: 100%;
                padding: 0 10px;
            }

            .puzzle-nav-item {
                width: 80px;
                height: 60px;
            }

            .puzzle-nav-item span {
                font-size: 9px;
            }

            .master-puzzle {
                width: 150px;
                height: 150px;
            }

            .hero-text {
                font-size: clamp(1.5rem, 5vw, 3rem);
            }
        }

        /* Loading Animation */
        .loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--dark);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loader.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loader-puzzle {
            width: 100px;
            height: 100px;
            position: relative;
            animation: spin 2s linear infinite;
        }

        .loader-puzzle::before,
        .loader-puzzle::after {
            content: '🧩';
            position: absolute;
            font-size: 50px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .loader-puzzle::after {
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
        }

        /* Counter Animation */
        .counter {
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loader" id="loader">
        <div class="loader-puzzle"></div>
    </div>

    <!-- Animated Background -->
    <div class="bg-animation"></div>

    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Puzzle Navigation -->
    <nav id="navigation">
        <div class="puzzle-nav-item" data-section="home">
            <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
                <path d="M 10,40 C 10,20 30,20 30,20 L 30,10 C 30,10 30,5 35,5 L 45,5 C 50,5 50,10 50,10 L 50,20 C 50,20 70,20 70,40 C 70,60 50,60 50,60 L 50,70 C 50,70 50,75 45,75 L 35,75 C 30,75 30,70 30,70 L 30,60 C 30,60 10,60 10,40 Z" 
                      fill="#00d4ff" stroke="#fff" stroke-width="2"/>
            </svg>
            <span>Home</span>
        </div>
        
        <div class="puzzle-nav-item" data-section="architecture">
            <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
                <path d="M 0,40 C 0,20 20,20 20,20 L 20,30 C 20,30 40,30 40,30 L 40,10 C 40,10 40,5 45,5 L 55,5 C 60,5 60,10 60,10 L 60,30 C 60,30 80,30 80,50 C 80,70 60,70 60,70 L 60,60 C 60,60 40,60 40,60 L 40,75 C 40,75 20,75 20,60 L 20,50 C 20,50 0,50 0,40 Z" 
                      fill="#ff6b6b" stroke="#fff" stroke-width="2"/>
            </svg>
            <span>Architecture</span>
        </div>
        
        <div class="puzzle-nav-item" data-section="google-cloud">
            <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
                <path d="M 10,30 C 10,10 30,10 30,10 L 30,20 C 30,20 50,20 50,20 L 50,5 C 50,5 70,5 70,20 L 70,30 C 70,30 90,30 90,30 L 90,10 C 90,10 90,5 95,5 L 105,5 C 110,5 110,10 110,10 L 110,30 C 110,50 90,50 90,50 L 90,60 C 90,60 90,75 70,75 L 70,60 C 70,60 50,60 50,60 L 50,75 C 50,75 30,75 30,60 L 30,50 C 30,50 10,50 10,30 Z" 
                      fill="#4ecdc4" stroke="#fff" stroke-width="2"/>
            </svg>
            <span>Google Cloud</span>
        </div>
        
        <div class="puzzle-nav-item" data-section="data-sources">
            <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
                <path d="M 20,40 C 20,20 40,20 40,20 L 40,10 C 40,10 40,5 45,5 L 55,5 C 60,5 60,10 60,10 L 60,20 C 60,20 80,20 80,20 L 80,5 C 80,5 100,5 100,20 L 100,40 C 100,60 80,60 80,60 L 80,70 C 80,70 80,75 75,75 L 65,75 C 60,75 60,70 60,70 L 60,60 C 60,60 40,60 40,60 L 40,75 C 40,75 20,75 20,60 Z" 
                      fill="#9b59b6" stroke="#fff" stroke-width="2"/>
            </svg>
            <span>Data Sources</span>
        </div>
        
        <div class="puzzle-nav-item" data-section="government">
            <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
                <path d="M 10,20 C 10,5 30,5 30,5 L 30,15 C 30,15 50,15 50,15 L 50,10 C 50,10 50,5 55,5 L 65,5 C 70,5 70,10 70,10 L 70,15 C 70,15 90,15 90,15 L 90,5 C 90,5 110,5 110,20 L 110,60 C 110,75 90,75 90,75 L 90,65 C 90,65 70,65 70,65 L 70,70 C 70,70 70,75 65,75 L 55,75 C 50,75 50,70 50,70 L 50,65 C 50,65 30,65 30,65 L 30,75 C 30,75 10,75 10,60 Z" 
                      fill="#f39c12" stroke="#fff" stroke-width="2"/>
            </svg>
            <span>Government</span>
        </div>
        
        <div class="puzzle-nav-item" data-section="privacy-compliance">
            <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
                <path d="M 0,20 C 0,5 20,5 20,5 L 20,10 C 20,10 20,5 25,5 L 35,5 C 40,5 40,10 40,10 L 40,15 C 40,15 60,15 60,15 L 60,5 C 60,5 80,5 80,15 C 80,15 100,15 100,35 C 100,55 80,55 80,55 L 80,60 C 80,60 80,65 75,65 L 65,65 C 60,65 60,60 60,60 L 60,55 C 60,55 40,55 40,55 L 40,65 C 40,65 40,70 35,70 L 25,70 C 20,70 20,65 20,65 L 20,55 C 20,55 0,55 0,35 Z" 
                      fill="#27ae60" stroke="#fff" stroke-width="2"/>
            </svg>
            <span>Privacy</span>
        </div>
        
        <div class="puzzle-nav-item" data-section="cost-benefit">
            <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
                <path d="M 10,40 C 10,20 30,20 30,20 L 30,5 C 30,5 50,5 50,20 L 50,25 C 50,25 70,25 70,25 L 70,10 C 70,10 70,5 75,5 L 85,5 C 90,5 90,10 90,10 L 90,25 C 90,25 110,25 110,45 C 110,65 90,65 90,65 L 90,75 C 90,75 70,75 70,60 L 70,55 C 70,55 50,55 50,55 L 50,60 C 50,60 50,75 30,75 L 30,60 C 30,60 10,60 10,40 Z" 
                      fill="#e74c3c" stroke="#fff" stroke-width="2"/>
            </svg>
            <span>Analysis</span>
        </div>
    </nav>

    <!-- Master Puzzle (builds as user scrolls) -->
    <div class="master-puzzle">
        <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <!-- Puzzle pieces that will be revealed as user progresses -->
            <g class="puzzle-piece" id="piece-1" data-section="architecture">
                <path d="M 50,50 L 100,50 L 100,100 L 50,100 Z" fill="#ff6b6b" stroke="#fff" stroke-width="2"/>
            </g>
            <g class="puzzle-piece" id="piece-2" data-section="google-cloud">
                <path d="M 100,50 L 150,50 L 150,100 L 100,100 Z" fill="#4ecdc4" stroke="#fff" stroke-width="2"/>
            </g>
            <g class="puzzle-piece" id="piece-3" data-section="data-sources">
                <path d="M 50,100 L 100,100 L 100,150 L 50,150 Z" fill="#9b59b6" stroke="#fff" stroke-width="2"/>
            </g>
            <g class="puzzle-piece" id="piece-4" data-section="government">
                <path d="M 100,100 L 150,100 L 150,150 L 100,150 Z" fill="#f39c12" stroke="#fff" stroke-width="2"/>
            </g>
        </svg>
    </div>

    <!-- Hero Section with Animation Sequence -->
    <section class="hero" id="home">
        <div class="hero-sequence" id="heroSequence">
            <div class="hero-text" data-delay="0">
                What is the most valuable asset in the world?
                <span class="subtitle highlight">Data</span>
            </div>
            <div class="hero-text" data-delay="4000">
                The Problem: Agencies crowdsource information to identify suspects.
                <span class="subtitle highlight">Time is ticking</span>
            </div>
            <div class="hero-text" data-delay="8000">
                The Vision:
                <span class="subtitle highlight">Make America Safe Again.</span>
            </div>
            <div class="hero-text" data-delay="12000">
                The Solution:
                <span class="subtitle highlight">Data is a puzzle. Piece the puzzle.</span>
            </div>
            <div class="hero-text" data-delay="16000">
                <span class="highlight">FaceTraceGov</span>
                <span class="subtitle">It's Highway Robbery</span>
            </div>
        </div>
    </section>

    <!-- Architecture Plan -->
    <section id="architecture">
        <div class="section-header">
            <h2>FaceTrace Architecture Plan</h2>
            <p>The foundation piece: Operating like advanced facial recognition search engines</p>
        </div>

        <div class="architecture-puzzle">
            <svg viewBox="0 0 800 400" style="width: 100%; height: 100%;">
                <!-- Central Hub -->
                <circle cx="400" cy="200" r="80" fill="var(--dark-light)" stroke="var(--primary)" stroke-width="3"/>
                <text x="400" y="205" text-anchor="middle" fill="var(--text)" font-size="16">FaceTrace Core</text>
                
                <!-- Input Layer -->
                <rect x="50" y="150" width="150" height="100" fill="var(--dark-lighter)" stroke="var(--secondary)" stroke-width="2" rx="10"/>
                <text x="125" y="200" text-anchor="middle" fill="var(--text)" font-size="14">Image Input</text>
                
                <!-- Processing Layer -->
                <rect x="600" y="50" width="150" height="80" fill="var(--dark-lighter)" stroke="var(--accent)" stroke-width="2" rx="10"/>
                <text x="675" y="90" text-anchor="middle" fill="var(--text)" font-size="14">ML Processing</text>
                
                <!-- Database Layer -->
                <rect x="600" y="270" width="150" height="80" fill="var(--dark-lighter)" stroke="var(--purple)" stroke-width="2" rx="10"/>
                <text x="675" y="310" text-anchor="middle" fill="var(--text)" font-size="14">Data Aggregation</text>
                
                <!-- Connections -->
                <path d="M 200 200 L 320 200" stroke="var(--primary)" stroke-width="2" marker-end="url(#arrowhead)"/>
                <path d="M 480 200 L 600 90" stroke="var(--primary)" stroke-width="2" marker-end="url(#arrowhead)"/>
                <path d="M 480 200 L 600 310" stroke="var(--primary)" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="var(--primary)"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="grid grid-2">
            <div class="puzzle-card">
                <h3>Core Recognition Engine</h3>
                <p>Advanced facial recognition technology that processes millions of images with high accuracy:</p>
                <ul class="feature-list">
                    <li>Real-time facial detection and encoding</li>
                    <li>Multi-angle recognition capability</li>
                    <li>Age progression algorithms</li>
                    <li>Partial face matching</li>
                </ul>
            </div>

            <div class="puzzle-card">
                <h3>Search Aggregation</h3>
                <p>Comprehensive search across multiple platforms and databases:</p>
                <ul class="feature-list">
                    <li>Cross-platform identity matching</li>
                    <li>Social media integration</li>
                    <li>Public records correlation</li>
                    <li>News and media scanning</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Google Cloud Integration -->
    <section id="google-cloud">
        <div class="section-header">
            <h2>Google Cloud Integration</h2>
            <p>The technology piece: Enterprise-grade infrastructure for unlimited scale</p>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number">99.9%</span>
                <span class="stat-label">Uptime SLA</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">50ms</span>
                <span class="stat-label">Average Latency</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">50%</span>
                <span class="stat-label">Cost Reduction</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">∞</span>
                <span class="stat-label">Scalability</span>
            </div>
        </div>

        <div class="puzzle-card">
            <h3>Vision API Integration</h3>
            <div class="code-block">
                <pre><span class="comment">// Google Cloud Vision API Integration</span>
<span class="keyword">import</span> { ImageAnnotatorClient } <span class="keyword">from</span> <span class="string">'@google-cloud/vision'</span>;

<span class="keyword">async function</span> <span class="function">detectFaces</span>(imageBuffer: Buffer) {
  <span class="keyword">const</span> [result] = <span class="keyword">await</span> visionClient.<span class="function">faceDetection</span>({
    image: { content: imageBuffer },
    features: [{
      type: <span class="string">'FACE_DETECTION'</span>,
      maxResults: 10,
      model: <span class="string">'builtin/latest'</span>
    }]
  });
  
  <span class="keyword">return</span> processForGovernment(result.faceAnnotations);
}</pre>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="puzzle-card">
                <h3>Infrastructure Benefits</h3>
                <ul class="feature-list">
                    <li>Auto-scaling Kubernetes clusters</li>
                    <li>Global CDN distribution</li>
                    <li>AI/ML optimized hardware</li>
                    <li>Enterprise security compliance</li>
                </ul>
            </div>

            <div class="puzzle-card">
                <h3>Advanced Features</h3>
                <ul class="feature-list">
                    <li>Batch processing capabilities</li>
                    <li>Real-time streaming analysis</li>
                    <li>Multi-region redundancy</li>
                    <li>Automated backup systems</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Third-Party Data Sources -->
    <section id="data-sources">
        <div class="section-header">
            <h2>Third-Party Data Sources</h2>
            <p>The crucial pieces: Comprehensive data aggregation for complete investigations</p>
        </div>

        <div class="data-sources">
            <div class="data-source-piece">
                <h4>TLO</h4>
                <p>TransUnion's comprehensive database</p>
            </div>
            <div class="data-source-piece">
                <h4>IRB Search</h4>
                <p>Public records aggregation</p>
            </div>
            <div class="data-source-piece">
                <h4>Tracers</h4>
                <p>Skip tracing specialists</p>
            </div>
            <div class="data-source-piece">
                <h4>IDI Core</h4>
                <p>Identity verification platform</p>
            </div>
            <div class="data-source-piece">
                <h4>Clear</h4>
                <p>Thomson Reuters CLEAR</p>
            </div>
            <div class="data-source-piece">
                <h4>Accurint</h4>
                <p>LexisNexis investigation tool</p>
            </div>
        </div>

        <div class="puzzle-card">
            <h3>Unified Data Platform</h3>
            <p>All these data sources come together to create a comprehensive investigation platform:</p>
            <div class="grid grid-2">
                <div>
                    <h4>Identity Verification</h4>
                    <ul class="feature-list">
                        <li>Cross-reference multiple databases</li>
                        <li>Real-time identity confirmation</li>
                        <li>Historical data analysis</li>
                    </ul>
                </div>
                <div>
                    <h4>Investigation Tools</h4>
                    <ul class="feature-list">
                        <li>Relationship mapping</li>
                        <li>Asset discovery</li>
                        <li>Location tracking</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Government Integration -->
    <section id="government">
        <div class="section-header">
            <h2>Government Integration</h2>
            <p>The security piece: FedTrace, StateTrace, and CountyTrace</p>
        </div>

        <div class="grid grid-3">
            <div class="puzzle-card">
                <h3>FedTrace</h3>
                <p>Federal agency integration:</p>
                <ul class="feature-list">
                    <li>FBI CJIS compliance</li>
                    <li>Air-gapped deployment</li>
                    <li>FIPS 140-2 encryption</li>
                    <li>Audit logging</li>
                </ul>
                <div class="stat-card" style="margin-top: 1rem;">
                    <span class="stat-number">$50M</span>
                    <span class="stat-label">Contract Potential</span>
                </div>
            </div>

            <div class="puzzle-card">
                <h3>StateTrace</h3>
                <p>State-level law enforcement:</p>
                <ul class="feature-list">
                    <li>State database integration</li>
                    <li>DMV photo matching</li>
                    <li>Missing persons alerts</li>
                    <li>Amber Alert system</li>
                </ul>
                <div class="stat-card" style="margin-top: 1rem;">
                    <span class="stat-number">50</span>
                    <span class="stat-label">States Available</span>
                </div>
            </div>

            <div class="puzzle-card">
                <h3>CountyTrace</h3>
                <p>Local law enforcement:</p>
                <ul class="feature-list">
                    <li>Local PD integration</li>
                    <li>Court system access</li>
                    <li>Jail booking photos</li>
                    <li>Community alerts</li>
                </ul>
                <div class="stat-card" style="margin-top: 1rem;">
                    <span class="stat-number">3,000+</span>
                    <span class="stat-label">Counties</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Privacy & Compliance -->
    <section id="privacy-compliance">
        <div class="section-header">
            <h2>Privacy & Compliance</h2>
            <p>The trust piece: Building confidence through comprehensive compliance</p>
        </div>

        <div class="puzzle-card">
            <h3>Compliance Framework</h3>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <thead>
                        <tr style="background: var(--dark-lighter);">
                            <th style="padding: 1rem; text-align: left; border: 1px solid var(--dark-lighter);">Regulation</th>
                            <th style="padding: 1rem; text-align: left; border: 1px solid var(--dark-lighter);">Status</th>
                            <th style="padding: 1rem; text-align: left; border: 1px solid var(--dark-lighter);">Implementation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">CJIS Security Policy</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter); color: var(--accent);">✓ Compliant</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">Full FBI standards implementation</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">FedRAMP</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter); color: var(--primary);">In Progress</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">Federal cloud security authorization</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">State Privacy Laws</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter); color: var(--accent);">✓ Compliant</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">All 50 states covered</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">Constitutional Standards</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter); color: var(--accent);">✓ Compliant</td>
                            <td style="padding: 1rem; border: 1px solid var(--dark-lighter);">4th Amendment protections</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="puzzle-card">
                <h3>Privacy-First Features</h3>
                <ul class="feature-list">
                    <li>End-to-end encryption</li>
                    <li>Automatic data expiration</li>
                    <li>Warrant requirement system</li>
                    <li>Audit trail blockchain</li>
                    <li>Zero-knowledge architecture</li>
                </ul>
            </div>

            <div class="puzzle-card">
                <h3>Security Measures</h3>
                <ul class="feature-list">
                    <li>Multi-factor authentication</li>
                    <li>Role-based access control</li>
                    <li>Continuous monitoring</li>
                    <li>Penetration testing</li>
                    <li>Incident response team</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Cost-Benefit Analysis -->
    <section id="cost-benefit">
        <div class="section-header">
            <h2>Cost-Benefit Analysis</h2>
            <p>The value piece: ROI for government agencies</p>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number counter" data-target="75">0</span>
                <span class="stat-label">Time Saved</span>
            </div>
            <div class="stat-card">
                <span class="stat-number counter" data-target="90">0</span>
                <span class="stat-label">Accuracy Rate</span>
            </div>
            <div class="stat-card">
                <span class="stat-number counter" data-target="10">0</span>
                <span class="stat-label">ROI Multiple</span>
            </div>
            <div class="stat-card">
                <span class="stat-number counter" data-target="24">0</span>
                <span class="stat-label">Hour Support</span>
            </div>
        </div>

        <div class="puzzle-card">
            <h3>Agency Benefits</h3>
            <div class="grid grid-2">
                <div>
                    <h4>Operational Efficiency</h4>
                    <ul class="feature-list">
                        <li>Reduce investigation time by 75%</li>
                        <li>Increase case closure rates</li>
                        <li>Minimize false positives</li>
                        <li>Streamline workflows</li>
                    </ul>
                </div>
                <div>
                    <h4>Cost Savings</h4>
                    <ul class="feature-list">
                        <li>Lower personnel costs</li>
                        <li>Reduced travel expenses</li>
                        <li>Consolidated data sources</li>
                        <li>Predictable pricing model</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="puzzle-card" style="background: var(--gradient-1); color: white; text-align: center;">
            <h3 style="color: white; font-size: 2rem;">The Complete Picture</h3>
            <p style="font-size: 1.2rem; margin: 1rem 0;">When all the pieces come together, FaceTraceGov provides government agencies with the most comprehensive facial recognition and investigation platform available.</p>
            <div style="margin-top: 2rem;">
                <button class="btn" style="background: white; color: var(--dark);">Schedule Demo</button>
            </div>
        </div>
    </section>

    <script>
        // Remove loader
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loader').classList.add('hidden');
                startHeroSequence();
            }, 1000);
        });

        // Hero sequence animation
        function startHeroSequence() {
            const texts = document.querySelectorAll('.hero-text');
            let currentIndex = 0;

            function showNextText() {
                if (currentIndex > 0) {
                    texts[currentIndex - 1].classList.remove('active');
                }
                
                if (currentIndex < texts.length) {
                    texts[currentIndex].classList.add('active');
                    currentIndex++;
                    
                    if (currentIndex < texts.length) {
                        const delay = parseInt(texts[currentIndex].dataset.delay) - parseInt(texts[currentIndex - 1].dataset.delay);
                        setTimeout(showNextText, delay);
                    }
                }
            }

            showNextText();
        }

        // Progress bar
        window.addEventListener('scroll', () => {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';
            
            // Update master puzzle
            updateMasterPuzzle();
        });

        // Puzzle navigation
        const navItems = document.querySelectorAll('.puzzle-nav-item');
        const sections = document.querySelectorAll('section[id]');

        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const sectionId = item.dataset.section;
                document.getElementById(sectionId).scrollIntoView({ behavior: 'smooth' });
            });
        });

        // Update active navigation
        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.dataset.section === current) {
                    item.classList.add('active');
                }
            });
        });

        // Update master puzzle based on scroll
        function updateMasterPuzzle() {
            const puzzlePieces = document.querySelectorAll('.puzzle-piece');
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                const sectionId = section.getAttribute('id');
                
                if (pageYOffset >= sectionTop - window.innerHeight / 2) {
                    puzzlePieces.forEach(piece => {
                        if (piece.dataset.section === sectionId) {
                            piece.classList.add('revealed');
                        }
                    });
                }
            });
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        sections.forEach(section => {
            observer.observe(section);
        });

        // Counter animation
        const counters = document.querySelectorAll('.counter');
        const speed = 200;

        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                    entry.target.classList.add('counted');
                    const target = +entry.target.getAttribute('data-target');
                    const increment = target / speed;
                    
                    const updateCount = () => {
                        const count = +entry.target.innerText;
                        if (count < target) {
                            entry.target.innerText = Math.ceil(count + increment);
                            setTimeout(updateCount, 1);
                        } else {
                            entry.target.innerText = target + (target === 75 || target === 90 ? '%' : target === 10 ? 'x' : '/7');
                        }
                    };
                    
                    updateCount();
                }
            });
        }, { threshold: 0.5 });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    </script>
</body>
</html>