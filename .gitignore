# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
package-lock.json
test
/test
/notes

# testing
/coverage
/test-results/
/playwright-report/
/playwright/.cache/

# next.js
/.next/
/out/
.next-*.json
next-env.d.ts
.swc/

combined_project.txt

# production
/build
/dist

# Database
neon_backup.sql
*.sqlite
*.sqlite3
*.db
.DS_Store

/memory-bank/

/memory-bank/*

# misc
.DS_Store
*.pem
Thumbs.db
.directory
*.log

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# environment variables
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# local files
*.local

# IDE
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.sublime-project
*.sublime-workspace

# vercel
.vercel

# typescript
*.tsbuildinfo

# Sentry Config File
.env.sentry-build-plugin

# Logs and backups
/logs
*.log
*.bak
*.backup
*.old

# OS-specific
.DS_Store
.AppleDouble
.LSOverride
._*
Thumbs.db
ehthumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
.cache/

.notes.md
notes.md
notes.md/*