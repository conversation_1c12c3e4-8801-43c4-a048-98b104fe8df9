<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630" fill="none">
  <!-- Enhanced background with modern gradients -->
  <defs>
    <!-- Background gradient using exact site colors -->
    <linearGradient id="bg-gradient" x1="0" y1="0" x2="1200" y2="630" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#0F172A" /> <!-- Site background -->
      <stop offset="50%" stop-color="#1E293B" /> <!-- Site input color -->
      <stop offset="100%" stop-color="#0F172A" /> <!-- Site background -->
    </linearGradient>
    
    <!-- Primary brand gradient -->
    <linearGradient id="brand-gradient" x1="0%" y1="0%" x2="100%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#5158f6" />
      <stop offset="30%" stop-color="#7C81FF" />
      <stop offset="70%" stop-color="#5158f6" />
      <stop offset="100%" stop-color="#3D40CC" />
    </linearGradient>
    
    <!-- Text gradient using site foreground -->
    <linearGradient id="text-shine" x1="0%" y1="0%" x2="100%" y2="0%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#F8FAFC" /> <!-- Site foreground -->
      <stop offset="50%" stop-color="#FFFFFF" />
      <stop offset="100%" stop-color="#F8FAFC" /> <!-- Site foreground -->
    </linearGradient>
    
    <!-- Glow effects -->
    <filter id="intense-glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="15" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <filter id="soft-glow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur stdDeviation="8" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- Grid pattern using site border color -->
    <pattern id="tech-grid" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="#334155" stroke-width="0.8" stroke-opacity="0.3"/>
      <circle cx="0" cy="0" r="1" fill="#5158f6" fill-opacity="0.2" />
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)" />
  
  <!-- Tech grid overlay -->
  <rect width="1200" height="630" fill="url(#tech-grid)" />
  
  <!-- Floating particles for depth -->
  <circle cx="120" cy="80" r="3" fill="#5158f6" fill-opacity="0.4">
    <animate attributeName="cy" values="80;100;80" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1080" cy="120" r="2" fill="#7C81FF" fill-opacity="0.5">
    <animate attributeName="cy" values="120;140;120" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="550" r="2.5" fill="#3D40CC" fill-opacity="0.6">
    <animate attributeName="cy" values="550;530;550" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1000" cy="500" r="1.5" fill="#5158f6" fill-opacity="0.3">
    <animate attributeName="cy" values="500;520;500" dur="3.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Main content container -->
  <g transform="translate(600, 315)">
    <!-- Central logo with enhanced design -->
    <g transform="translate(-400, -100)">
      <!-- Logo background aura -->
      <circle cx="100" cy="100" r="120" fill="url(#brand-gradient)" fill-opacity="0.08" filter="url(#intense-glow)" />
      
      <!-- Main logo -->
      <g filter="url(#soft-glow)">
        <!-- Face outline - enhanced geometric design -->
        <polygon 
          points="100,30 70,50 55,100 70,150 100,170 130,150 145,100 130,50" 
          stroke="white" 
          stroke-width="6" 
          fill="url(#brand-gradient)"
          fill-opacity="0.2"
          stroke-linejoin="round"
        />
        
        <!-- Advanced biometric grid -->
        <path d="M100,30 L100,170" stroke="#7C81FF" stroke-width="1.5" stroke-opacity="0.8" stroke-dasharray="6 4" />
        <path d="M55,100 L145,100" stroke="#7C81FF" stroke-width="1.5" stroke-opacity="0.8" stroke-dasharray="6 4" />
        <path d="M70,50 L130,150" stroke="#5158f6" stroke-width="1.2" stroke-opacity="0.7" stroke-dasharray="4 3" />
        <path d="M70,150 L130,50" stroke="#5158f6" stroke-width="1.2" stroke-opacity="0.7" stroke-dasharray="4 3" />
        
        <!-- Scanning lines effect -->
        <path d="M75,75 L125,75" stroke="#7C81FF" stroke-width="0.8" stroke-opacity="0.6" stroke-dasharray="2 1" />
        <path d="M75,125 L125,125" stroke="#7C81FF" stroke-width="0.8" stroke-opacity="0.6" stroke-dasharray="2 1" />
        
        <!-- Modern eyes with tech styling -->
        <rect x="80" y="85" width="12" height="10" rx="3" stroke="#7C81FF" stroke-width="3.5" fill="url(#brand-gradient)" fill-opacity="0.3" />
        <rect x="108" y="85" width="12" height="10" rx="3" stroke="#7C81FF" stroke-width="3.5" fill="url(#brand-gradient)" fill-opacity="0.3" />
        
        <!-- Mouth - straight line for serious expression -->
        <path d="M80,125 L120,125" stroke="#7C81FF" stroke-width="3" fill="none" stroke-linecap="round" />
        
        <!-- Biometric measurement points with animation -->
        <circle cx="100" cy="70" r="2" fill="#5158f6">
          <animate attributeName="r" values="2;3;2" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="100" cy="130" r="2" fill="#5158f6">
          <animate attributeName="r" values="2;3;2" dur="2s" begin="0.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="75" cy="100" r="2" fill="#5158f6">
          <animate attributeName="r" values="2;3;2" dur="2s" begin="1s" repeatCount="indefinite"/>
        </circle>
        <circle cx="125" cy="100" r="2" fill="#5158f6">
          <animate attributeName="r" values="2;3;2" dur="2s" begin="1.5s" repeatCount="indefinite"/>
        </circle>
      </g>
    </g>
    
    <!-- Brand text with enhanced typography -->
    <g transform="translate(0, -50)">
      <!-- Main title -->
      <text x="0" y="0" font-family="'Electrolize', 'SF Mono', monospace" font-size="96" font-weight="700" fill="url(#text-shine)" text-anchor="middle" filter="url(#soft-glow)">FaceTrace</text>
      
      <!-- Animated separator dot -->
      <circle cx="220" cy="-20" r="5" fill="#5158f6">
        <animate attributeName="opacity" values="1;0.4;1" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="r" values="5;7;5" dur="2s" repeatCount="indefinite"/>
      </circle>
      
      <!-- Pro text -->
      <text x="0" y="80" font-family="'Electrolize', 'SF Mono', monospace" font-size="56" font-weight="600" fill="url(#brand-gradient)" text-anchor="middle">Pro</text>
    </g>
    
    <!-- Enhanced tagline -->
    <text x="0" y="80" font-family="system-ui, -apple-system, sans-serif" font-size="36" fill="white" fill-opacity="0.9" text-anchor="middle" font-weight="300">Find Where Faces Appear Across The Web</text>
    
    <!-- Feature highlights with modern icons -->
    <g transform="translate(-200, 140)">
      <!-- AI-Powered -->
      <g>
        <circle cx="40" cy="40" r="35" fill="url(#brand-gradient)" fill-opacity="0.15" stroke="#5158f6" stroke-width="2" stroke-opacity="0.6" />
        <path d="M25,35 Q35,20 45,30 Q55,35 45,50 Q35,60 25,50 Q15,40 25,35" stroke="#7C81FF" stroke-width="3" fill="none" stroke-linecap="round" />
        <circle cx="35" cy="35" r="2.5" fill="#5158f6" />
        <circle cx="45" cy="45" r="2" fill="#7C81FF" />
        <text x="100" y="48" font-family="'Electrolize', system-ui, sans-serif" font-size="22" fill="white" font-weight="500">AI-Powered Search</text>
      </g>
    </g>
    
    <g transform="translate(50, 140)">
      <!-- Real-time -->
      <g>
        <circle cx="40" cy="40" r="35" fill="url(#brand-gradient)" fill-opacity="0.15" stroke="#5158f6" stroke-width="2" stroke-opacity="0.6" />
        <path d="M30,20 L25,40 L40,35 L30,60 L45,40 L30,45 Z" stroke="#7C81FF" stroke-width="3" fill="#5158f6" fill-opacity="0.4" stroke-linejoin="round" />
        <text x="100" y="48" font-family="'Electrolize', system-ui, sans-serif" font-size="22" fill="white" font-weight="500">Real-time Results</text>
      </g>
    </g>
    
    <!-- Website URL with modern styling -->
    <text x="0" y="220" font-family="'Electrolize', 'SF Mono', monospace" font-size="28" fill="#7C81FF" fill-opacity="0.9" text-anchor="middle" font-weight="500">facetrace.pro</text>
  </g>
  
  <!-- Corner accent elements -->
  <path d="M0,0 L60,0 L0,60 Z" fill="url(#brand-gradient)" fill-opacity="0.1" />
  <path d="M1200,630 L1140,630 L1200,570 Z" fill="url(#brand-gradient)" fill-opacity="0.1" />
</svg>
