<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OG Image Converter</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #0F172A;
            color: white;
        }
        .container {
            display: grid;
            gap: 2rem;
        }
        .preview {
            border: 2px solid #5158f6;
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }
        .preview img {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        button {
            background: linear-gradient(135deg, #5158f6, #7C81FF);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        button:hover {
            transform: scale(1.05);
        }
        .instructions {
            background: rgba(81, 88, 246, 0.1);
            border: 1px solid #5158f6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        h1 {
            background: linear-gradient(135deg, #5158f6, #7C81FF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <h1>FaceTrace OG Image Converter</h1>
    
    <div class="instructions">
        <h3>✅ Fixed Issues:</h3>
        <ul>
            <li><strong>Color Scheme:</strong> Now uses exact site colors (#5158f6, #3D40CC, #7C81FF, #0F172A, #F8FAFC)</li>
            <li><strong>Face Design:</strong> Removed smile - now has serious straight-line mouth</li>
            <li><strong>Alignment:</strong> Uses correct positioning from twitter-image.svg</li>
            <li><strong>Grid Pattern:</strong> Uses site border color (#334155) for subtle background</li>
        </ul>
        <h3>Instructions:</h3>
        <ol>
            <li>Use the buttons below to download PNG versions</li>
            <li>Or right-click on the preview images and "Save image as..."</li>
            <li>Replace the existing og-image.png files in your public folder</li>
        </ol>
    </div>

    <div class="container">
        <div>
            <h2>Main OG Image (1200x630)</h2>
            <div class="preview">
                <img id="og-main" src="og-image-modern.svg" alt="FaceTrace OG Image">
            </div>
            <div class="controls">
                <button onclick="downloadPNG('og-main', 'og-image.png', 1200, 630)">Download PNG</button>
                <button onclick="window.open('og-image-modern.svg', '_blank')">View SVG</button>
            </div>
        </div>

        <div>
            <h2>Twitter Image (1200x630)</h2>
            <div class="preview">
                <img id="twitter" src="twitter-image.svg" alt="FaceTrace Twitter Image">
            </div>
            <div class="controls">
                <button onclick="downloadPNG('twitter', 'twitter-image.png', 1200, 630)">Download PNG</button>
                <button onclick="window.open('twitter-image.svg', '_blank')">View SVG</button>
            </div>
        </div>

        <div>
            <h2>Updated Original OG Image</h2>
            <div class="preview">
                <img id="og-original" src="og-image.svg" alt="FaceTrace Original OG Image">
            </div>
            <div class="controls">
                <button onclick="downloadPNG('og-original', 'og-image-updated.png', 1200, 630)">Download PNG</button>
                <button onclick="window.open('og-image.svg', '_blank')">View SVG</button>
            </div>
        </div>
    </div>

    <script>
        function downloadPNG(elementId, filename, width, height) {
            const img = document.getElementById(elementId);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = width;
            canvas.height = height;
            
            // Create a new image to ensure it's loaded
            const newImg = new Image();
            newImg.onload = function() {
                ctx.drawImage(newImg, 0, 0, width, height);
                
                // Convert to PNG and download
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };
            
            newImg.src = img.src;
        }

        // Add some visual feedback
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('load', function() {
                    this.style.opacity = '1';
                    this.style.transition = 'opacity 0.5s ease';
                });
                img.style.opacity = '0.7';
            });
        });
    </script>
</body>
</html>
