'use client';

import React, { useRef, useCallback, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Declare global grecaptcha for TypeScript
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
      enterprise?: {
        ready: (callback: () => void) => void;
        execute: (siteKey: string, options: { action: string }) => Promise<string>;
      };
    };
  }
}

interface CaptchaVerificationProps {
  onVerify: (token: string | null) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  action?: string;
  className?: string;
  autoExecute?: boolean;
  onReset?: () => void;
}

export const CaptchaVerification: React.FC<CaptchaVerificationProps> = ({
  onVerify,
  onError,
  disabled = false,
  action = 'FACETRACE_SEARCH',
  className = '',
  autoExecute = false,
  onReset
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scriptLoaded, setScriptLoaded] = useState(false);

  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

  // Load reCAPTCHA script (Enterprise or standard based on site key)
  useEffect(() => {
    if (!siteKey) {
      const errorMsg = 'reCAPTCHA site key not configured';
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    // Check if we should use Enterprise (only if not forced to use standard)
    const forceStandard = process.env.NEXT_PUBLIC_FORCE_STANDARD_RECAPTCHA === 'true';
    const hasProjectId = process.env.NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_ID;
    const isEnterprise = !forceStandard && hasProjectId;

    // Check if script is already loaded
    if (isEnterprise && window.grecaptcha?.enterprise?.ready) {
      setScriptLoaded(true);
      return;
    } else if (!isEnterprise && window.grecaptcha?.ready) {
      setScriptLoaded(true);
      return;
    }

    // Load the appropriate reCAPTCHA script
    const script = document.createElement('script');
    if (isEnterprise) {
      script.src = `https://www.google.com/recaptcha/enterprise.js?render=${siteKey}`;
      console.log('[captcha] Loading reCAPTCHA Enterprise script...');
    } else {
      script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
      console.log('[captcha] Loading reCAPTCHA v3 script...');
    }

    script.async = true;
    script.defer = true;

    script.onload = () => {
      setScriptLoaded(true);
      console.log(`[captcha] reCAPTCHA ${isEnterprise ? 'Enterprise' : 'v3'} script loaded successfully`);
    };

    script.onerror = () => {
      const errorMsg = 'Failed to load reCAPTCHA script';
      setError(errorMsg);
      onError?.(errorMsg);
      console.error('[captcha] Failed to load reCAPTCHA script');
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script on unmount
      const selector = isEnterprise ? 'script[src*="recaptcha/enterprise.js"]' : 'script[src*="recaptcha/api.js"]';
      const existingScript = document.querySelector(selector);
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, [siteKey, onError]);

  // Execute CAPTCHA verification
  const executeRecaptcha = useCallback(async () => {
    if (!scriptLoaded || disabled) {
      console.log('[captcha] Cannot execute: scriptLoaded:', scriptLoaded, 'disabled:', disabled);
      return;
    }

    // Check if we should use Enterprise (only if not forced to use standard)
    const forceStandard = process.env.NEXT_PUBLIC_FORCE_STANDARD_RECAPTCHA === 'true';
    const hasProjectId = process.env.NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_ID;
    const isEnterprise = !forceStandard && hasProjectId;

    // Verify the appropriate API is available
    if (isEnterprise && !window.grecaptcha?.enterprise) {
      console.log('[captcha] Enterprise API not available');
      return;
    } else if (!isEnterprise && !window.grecaptcha?.ready) {
      console.log('[captcha] Standard API not available');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (isEnterprise) {
        // Use Enterprise API with the pattern from Google's documentation
        console.log('[captcha] Executing reCAPTCHA Enterprise verification for action:', action);

        const token = await new Promise<string>((resolve, reject) => {
          window.grecaptcha.enterprise!.ready(async () => {
            try {
              const token = await window.grecaptcha.enterprise!.execute(siteKey!, {
                action: action
              });
              resolve(token);
            } catch (error) {
              reject(error);
            }
          });
        });

        if (token) {
          setIsVerified(true);
          setIsLoading(false);
          onVerify(token);
          console.log('[captcha] reCAPTCHA Enterprise verification successful, token length:', token.length);
        } else {
          throw new Error('No token received from reCAPTCHA Enterprise');
        }
      } else {
        // Use standard API
        await new Promise<void>((resolve) => {
          window.grecaptcha.ready(() => {
            resolve();
          });
        });

        console.log('[captcha] Executing reCAPTCHA v3 verification for action:', action);

        const token = await window.grecaptcha.execute(siteKey!, {
          action: action
        });

        if (token) {
          setIsVerified(true);
          setIsLoading(false);
          onVerify(token);
          console.log('[captcha] reCAPTCHA v3 verification successful, token length:', token.length);
        } else {
          throw new Error('No token received from reCAPTCHA v3');
        }
      }
    } catch (err) {
      const errorMsg = 'CAPTCHA verification failed. Please try again.';
      setError(errorMsg);
      setIsLoading(false);
      setIsVerified(false);
      onError?.(errorMsg);
      onVerify(null);
      console.error(`[captcha] reCAPTCHA ${isEnterprise ? 'Enterprise' : 'v3'} verification failed:`, err);
    }
  }, [scriptLoaded, disabled, siteKey, action, onVerify, onError]);

  // Auto-execute when script is loaded and autoExecute is true
  useEffect(() => {
    if (scriptLoaded && autoExecute && !isVerified && !disabled) {
      executeRecaptcha();
    }
  }, [scriptLoaded, autoExecute, isVerified, disabled, executeRecaptcha]);

  const reset = useCallback(() => {
    setIsVerified(false);
    setError(null);
    setIsLoading(false);
    onVerify(null);
    onReset?.();
    console.log('[captcha] CAPTCHA component reset - will generate new token on next verification');
  }, [onVerify, onReset]);

  if (!siteKey) {
    return (
      <div className={`p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg ${className}`}>
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span className="text-red-700 dark:text-red-300 text-sm font-medium">
            CAPTCHA configuration error
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`captcha-container ${className}`}>
      <div className="flex flex-col items-center space-y-3">
        {/* Manual verification button (for non-auto mode) */}
        {!autoExecute && (
          <motion.button
            onClick={executeRecaptcha}
            disabled={disabled || isLoading || isVerified}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              disabled || isLoading || isVerified
                ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg'
            }`}
            whileHover={!disabled && !isLoading && !isVerified ? { scale: 1.02 } : {}}
            whileTap={!disabled && !isLoading && !isVerified ? { scale: 0.98 } : {}}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Verifying...</span>
              </div>
            ) : isVerified ? (
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Verified</span>
              </div>
            ) : (
              'Verify Security'
            )}
          </motion.button>
        )}

        {/* Status Indicator for auto mode */}
        {autoExecute && (
          <AnimatePresence>
            {isLoading && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center space-x-2 text-blue-600 dark:text-blue-400"
              >
                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-sm font-medium">Verifying security...</span>
              </motion.div>
            )}

            {isVerified && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center space-x-2 text-green-600 dark:text-green-400"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium">Security verified</span>
              </motion.div>
            )}
          </AnimatePresence>
        )}

        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center space-x-2 text-red-600 dark:text-red-400"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span className="text-sm font-medium">{error}</span>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Security Notice */}
        <div className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400 max-w-sm">
            This invisible security verification helps protect against automated abuse and ensures legitimate usage of FaceTrace services.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CaptchaVerification;

// Hook for easier usage
export const useCaptcha = () => {
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [isVerified, setIsVerified] = useState(false);
  const [tokenUsed, setTokenUsed] = useState(false);

  const handleCaptchaVerify = useCallback((token: string | null) => {
    setCaptchaToken(token);
    setIsVerified(!!token);
    setTokenUsed(false); // Reset used flag when new token is received
    console.log('[useCaptcha] New CAPTCHA token received, length:', token?.length || 0);
  }, []);

  const resetCaptcha = useCallback(() => {
    setCaptchaToken(null);
    setIsVerified(false);
    setTokenUsed(false);
    console.log('[useCaptcha] CAPTCHA state reset');
  }, []);

  const markTokenAsUsed = useCallback(() => {
    setTokenUsed(true);
    console.log('[useCaptcha] CAPTCHA token marked as used');
  }, []);

  return {
    captchaToken,
    isVerified,
    tokenUsed,
    handleCaptchaVerify,
    resetCaptcha,
    markTokenAsUsed
  };
};
