'use client';

import React, { useState } from 'react';
import { CaptchaVerification } from './CaptchaVerification';

/**
 * Test component to verify reCAPTCHA Enterprise integration
 * This component can be used to test CAPTCHA functionality
 */
export const CaptchaTest: React.FC = () => {
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [verificationResult, setVerificationResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleCaptchaVerify = (token: string | null) => {
    setCaptchaToken(token);
    setError(null);
    
    if (token) {
      console.log('[captcha-test] Received token:', token.substring(0, 20) + '...');
      testBackendVerification(token);
    } else {
      setVerificationResult(null);
    }
  };

  const handleCaptchaError = (errorMsg: string) => {
    setError(errorMsg);
    console.error('[captcha-test] CAPTCHA error:', errorMsg);
  };

  const testBackendVerification = async (token: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/test-captcha', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          captchaToken: token,
          action: 'TEST_ACTION'
        }),
      });

      const result = await response.json();
      setVerificationResult(result);

      if (!response.ok) {
        setError(result.error || 'Backend verification failed');
      }
    } catch (err) {
      const errorMsg = 'Failed to test backend verification';
      setError(errorMsg);
      console.error('[captcha-test] Backend test failed:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const testBypassMode = async () => {
    setIsLoading(true);
    setError(null);
    setCaptchaToken('bypass-token-for-testing');

    try {
      const response = await fetch('/api/test-captcha', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          captchaToken: 'bypass-token-for-testing',
          action: 'TEST_ACTION'
        }),
      });

      const result = await response.json();
      setVerificationResult(result);

      if (!response.ok) {
        setError(result.error || 'Backend verification failed');
      }
    } catch (err) {
      const errorMsg = 'Failed to test bypass mode';
      setError(errorMsg);
      console.error('[captcha-test] Bypass test failed:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
        reCAPTCHA Enterprise Test
      </h2>

      <div className="space-y-6">
        {/* CAPTCHA Component */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
            Frontend CAPTCHA Verification
          </h3>
          <CaptchaVerification
            onVerify={handleCaptchaVerify}
            onError={handleCaptchaError}
            action="TEST_ACTION"
            className="w-full"
          />

          {/* Test Bypass Mode Button */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={testBypassMode}
              disabled={isLoading}
              className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Test Backend with Bypass Token
            </button>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              This tests the backend verification directly, bypassing frontend CAPTCHA generation
            </p>
          </div>
        </div>

        {/* Token Display */}
        {captchaToken && (
          <div className="border border-green-200 dark:border-green-700 rounded-lg p-4 bg-green-50 dark:bg-green-900/20">
            <h3 className="text-lg font-semibold mb-2 text-green-800 dark:text-green-200">
              ✅ Token Received
            </h3>
            <p className="text-sm text-green-700 dark:text-green-300 font-mono break-all">
              {captchaToken.substring(0, 50)}...
            </p>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
              Token length: {captchaToken.length} characters
            </p>
          </div>
        )}

        {/* Backend Verification Result */}
        {isLoading && (
          <div className="border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center">
              <svg className="animate-spin h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="text-blue-700 dark:text-blue-300">Testing backend verification...</span>
            </div>
          </div>
        )}

        {verificationResult && (
          <div className={`border rounded-lg p-4 ${
            verificationResult.success 
              ? 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20'
              : 'border-red-200 dark:border-red-700 bg-red-50 dark:bg-red-900/20'
          }`}>
            <h3 className={`text-lg font-semibold mb-2 ${
              verificationResult.success 
                ? 'text-green-800 dark:text-green-200'
                : 'text-red-800 dark:text-red-200'
            }`}>
              {verificationResult.success ? '✅ Backend Verification Successful' : '❌ Backend Verification Failed'}
            </h3>
            <pre className={`text-sm font-mono whitespace-pre-wrap ${
              verificationResult.success 
                ? 'text-green-700 dark:text-green-300'
                : 'text-red-700 dark:text-red-300'
            }`}>
              {JSON.stringify(verificationResult, null, 2)}
            </pre>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="border border-red-200 dark:border-red-700 rounded-lg p-4 bg-red-50 dark:bg-red-900/20">
            <h3 className="text-lg font-semibold mb-2 text-red-800 dark:text-red-200">
              ❌ Error
            </h3>
            <p className="text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Configuration Info */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20">
          <h3 className="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">
            Configuration Info
          </h3>
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Site Key:</strong> {process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY?.substring(0, 20)}...</p>
            <p><strong>Project ID:</strong> {process.env.NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_ID || 'Not configured'}</p>
            <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
            <p><strong>Force Standard:</strong> {process.env.NEXT_PUBLIC_FORCE_STANDARD_RECAPTCHA || 'false'}</p>
            <p><strong>Current Domain:</strong> {typeof window !== 'undefined' ? window.location.hostname : 'server-side'}</p>
          </div>

          {/* Domain Issue Warning */}
          {typeof window !== 'undefined' && window.location.hostname === 'localhost' && (
            <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded">
              <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                ⚠️ <strong>Domain Issue:</strong> You're testing on localhost, but the reCAPTCHA site key may be configured for specific domains.
                This might cause "browser-error" responses. The backend has been configured to allow this in development mode.
              </p>
            </div>
          )}

          {/* New Site Key Info */}
          <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded">
            <p className="text-blue-800 dark:text-blue-200 text-sm">
              ℹ️ <strong>Updated Configuration:</strong> Now using site key 6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaptchaTest;
