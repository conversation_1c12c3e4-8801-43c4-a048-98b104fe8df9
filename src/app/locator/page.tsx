'use client';

import { useState } from 'react';
import { useSignIn, useClerk } from '@clerk/nextjs';
import Header from '../components/Header';
import Footer from '../components/Footer';
import Link from 'next/link';

// Check if authentication is disabled
const isAuthDisabled = process.env.NEXT_PUBLIC_DISABLE_AUTH === 'true';

// Custom hook to safely use <PERSON> hooks when available
function useConditionalClerk() {
  if (isAuthDisabled) {
    return { signIn: null, client: null };
  }

  try {
    const signInHook = useSignIn();
    const clerkHook = useClerk();
    return { signIn: signInHook?.signIn || null, client: clerkHook?.client || null };
  } catch (error) {
    console.warn('[Report Locator] Clerk hooks failed, using defaults:', error);
    return { signIn: null, client: null };
  }
}

export default function ReportLocator() {
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [step, setStep] = useState<'email' | 'verify' | 'reports'>('email');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reports, setReports] = useState<any[]>([]);
  const { signIn } = useConditionalClerk();

  // If auth is disabled, show a message that this feature is not available
  if (isAuthDisabled) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
        <Header />

        <main className="flex-grow container mx-auto px-4 py-12">
          <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div className="p-6">
              <div className="flex justify-center mb-6">
                <div className="h-12 w-12 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
              </div>

              <h1 className="text-2xl font-bold text-center text-gray-900 dark:text-white mb-6">
                Report Locator
              </h1>

              <div className="text-center py-8">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Report locator is currently unavailable in this version.
                </p>
                <Link
                  href="/search"
                  className="inline-block bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white px-6 py-3 rounded-lg font-bold hover:opacity-90 transition-all"
                >
                  Start a new search
                </Link>
              </div>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  // Handle email submission
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    // Basic validation
    if (!email.trim() || !/\S+@\S+\.\S+/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Start the sign-in process with email
      await signIn?.create({
        identifier: email,
        strategy: 'email_code',
      });
      
      // Move to verification step
      setStep('verify');
    } catch (error: any) {
      console.error('Error starting email verification:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle verification code submission
  const handleVerifySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (!verificationCode.trim()) {
      setError('Please enter the verification code');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Attempt verification with the code
      const result = await signIn?.attemptFirstFactor({
        strategy: 'email_code',
        code: verificationCode,
      });
      
      if (result?.status === 'complete') {
        // Skip trying to set the session since this is just a verification
        // to allow viewing reports associated with this email

        // Fetch the user's reports
        await fetchReports();
        
        // Show reports
        setStep('reports');
      } else {
        setError('Verification failed. Please try again.');
      }
    } catch (error: any) {
      console.error('Error verifying code:', error);
      setError('Invalid code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch reports for the email
  const fetchReports = async () => {
    try {
      const response = await fetch('/api/data?action=reports-by-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch reports');
      }
      
      const data = await response.json();
      setReports(data.reports || []);
    } catch (error) {
      console.error('Error fetching reports:', error);
      setError('Failed to retrieve your reports. Please try again.');
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
      <Header />
      
      <main className="flex-grow container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <div className="p-6">
            <div className="flex justify-center mb-6">
              <div className="h-12 w-12 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
            
            <h1 className="text-2xl font-bold text-center text-gray-900 dark:text-white mb-6">
              {step === 'email' && 'Find Your Reports'}
              {step === 'verify' && 'Verify Your Email'}
              {step === 'reports' && 'Your Reports'}
            </h1>
            
            {/* Error message */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  <span>{error}</span>
                </div>
              </div>
            )}
            
            {/* Email Step */}
            {step === 'email' && (
              <form onSubmit={handleEmailSubmit} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="w-full h-10 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                    disabled={isLoading}
                    required
                  />
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Enter the email address you used for your reports. We'll send you a verification code.
                </p>
                
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full rounded-lg py-3 font-bold transition-all ${
                    isLoading
                      ? 'bg-gray-400 dark:bg-gray-600 text-white cursor-not-allowed'
                      : 'bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white hover:opacity-90'
                  }`}
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending...
                    </span>
                  ) : (
                    'Continue'
                  )}
                </button>
              </form>
            )}
            
            {/* Verification Step */}
            {step === 'verify' && (
              <form onSubmit={handleVerifySubmit} className="space-y-4">
                <div>
                  <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Verification Code
                  </label>
                  <input
                    type="text"
                    id="verificationCode"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    placeholder="Enter the 6-digit code"
                    className="w-full h-10 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                    disabled={isLoading}
                    required
                  />
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  We sent a verification code to {email}. Please enter it to view your reports.
                </p>
                
                <div className="flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => setStep('email')}
                    className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                    disabled={isLoading}
                  >
                    Back
                  </button>
                  
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={`rounded-lg py-2 px-4 font-bold transition-all ${
                      isLoading
                        ? 'bg-gray-400 dark:bg-gray-600 text-white cursor-not-allowed'
                        : 'bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white hover:opacity-90'
                    }`}
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Verifying...
                      </span>
                    ) : (
                      'Verify'
                    )}
                  </button>
                </div>
              </form>
            )}
            
            {/* Reports List */}
            {step === 'reports' && (
              <div className="space-y-4">
                {reports.length === 0 ? (
                  <div className="text-center py-8">
                    <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <p className="text-gray-600 dark:text-gray-400">
                      No reports found for this email address.
                    </p>
                    <Link
                      href="/search"
                      className="text-blue-600 dark:text-blue-400 hover:underline block mt-4"
                    >
                      Create a new search
                    </Link>
                  </div>
                ) : (
                  <>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Found <span className="font-bold">{reports.length}</span> search reports linked to your email.
                      </p>
                    </div>
                    
                    <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                      {reports.map((report) => (
                        <li key={report.id} className="py-4">
                          <Link href={`/r/${report.report_id || report.id}`} className="block hover:bg-gray-50 dark:hover:bg-gray-700/30 -mx-4 px-4 py-2 rounded-lg transition-colors">
                            <div className="flex justify-between">
                              <p className="font-medium text-gray-900 dark:text-white">
                                {report.title || `Search Report #${(report.report_id || report.id).substring(0, 8)}`}
                              </p>
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                report.status === 'completed' 
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                                  : report.status === 'processing'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'
                              }`}>
                                {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {formatDate(report.created_at)}
                            </p>
                            {report.result_count !== undefined && (
                              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {report.result_count} results found
                              </p>
                            )}
                          </Link>
                        </li>
                      ))}
                    </ul>
                    
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                      <Link
                        href="/search"
                        className="text-center block w-full py-2 px-4 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      >
                        Start a new search
                      </Link>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 