import imageCompression from 'browser-image-compression';

// Define the SearchResult type
export interface SearchResult {
  id: string;
  confidence: number;
  title?: string;
  sourceUrl: string;
  thumbnail?: string;
  domain?: string;
  sourceType?: string;
  description?: string;
  rawData?: any;
}

/**
 * Get the count of faces in the database
 */
export async function getFacesCount(): Promise<number> {
  // Default faces count if API call fails
  const DEFAULT_FACES_COUNT = 200000000; // 200 million as fallback 
  
  try {
    // Add timeout control
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
    
    const response = await fetch('/api/core?action=faces', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal
    });
    
    // Clear timeout since request completed
    clearTimeout(timeoutId);

    if (!response.ok) {
      console.warn(`[apiService] Failed to fetch faces count: ${response.status} ${response.statusText}`);
      return DEFAULT_FACES_COUNT;
    }

    const data = await response.json();

    // Check if the faces property exists and is a number
    if (data && typeof data.faces === 'number') {
      console.log(`[apiService] Successfully fetched faces count: ${data.faces.toLocaleString()}`);
      return data.faces;
    } 
    
    // Handle case where faces exists but isn't a number
    if (data && data.faces && typeof data.faces !== 'number') {
      // Try to parse it as a number
      const parsedCount = parseInt(data.faces);
      if (!isNaN(parsedCount)) {
        console.log(`[apiService] Parsed faces count from non-numeric response: ${parsedCount.toLocaleString()}`);
        return parsedCount;
      }
    }
    
    console.warn('[apiService] No valid faces count in API response:', data);
    return DEFAULT_FACES_COUNT;
  } catch (error) {
    // Check for timeout/abort specifically
    if (error instanceof DOMException && error.name === 'AbortError') {
      console.error('[apiService] Faces count request timed out after 10 seconds');
    } else {
      console.error('[apiService] Error fetching faces count:', error);
    }
    
    return DEFAULT_FACES_COUNT;
  }
}

/**
 * Convert HEIC to JPEG before uploading
 */
export const convertHeicToJpeg = async (file: File): Promise<File> => {
  if (file.type === 'image/heic' || file.name.toLowerCase().endsWith('.heic')) {
    try {
      // Dynamic import for better performance
      const heicConvert = (await import('heic-convert')).default;

      // Convert HEIC to JPEG
      const buffer = await file.arrayBuffer();
      const convertedBuffer = await heicConvert({
        buffer: Buffer.from(buffer),
        format: 'JPEG',
        quality: 0.9
      });

      // Create a new file from the converted buffer
      return new File([convertedBuffer], file.name.replace(/\.heic$/i, '.jpg'), {
        type: 'image/jpeg'
      });
    } catch (error) {
      console.error('Error converting HEIC to JPEG:', error);
      // Return the original file if conversion fails
      return file;
    }
  }

  // Return the original file if it's not HEIC
  return file;
};

/**
 * Compress image to specified size (default 6MB for API requirement)
 */
export const compressImage = async (file: File, maxSizeMB = 6): Promise<File> => {
  // Only compress if the file is larger than maxSizeMB
  if (file.size > maxSizeMB * 1024 * 1024 && file.type.startsWith('image/')) {
    try {
      // Compression options
      const options = {
        maxSizeMB,
        maxWidthOrHeight: 4000, // Reasonable size that maintains quality
        useWebWorker: true,
        fileType: file.type
      };

      // Compress the image
      return await imageCompression(file, options);
    } catch (error) {
      console.error('Error compressing image:', error);
      // Return the original file if compression fails
      return file;
    }
  }

  // Return the original file if no compression is needed
  return file;
};

/**
 * Upload an image to the API
 */
export async function uploadPic(file: File): Promise<string> {
  const formData = new FormData();
  formData.append('images', file);
  formData.append('id_search', '');
  
  // Store original file information
  const originalFileName = file.name;
  const originalFileType = file.type;
  const originalFileSize = file.size;

  const response = await fetch('/api/search?action=upload', {
    method: 'POST',
    body: formData
  });

  if (!response.ok) {
    throw new Error('Failed to upload image');
  }

  const data = await response.json();

  if (data.error) {
    throw new Error(data.error);
  }
  
  // If the response includes the ID search identifier, add file metadata
  // This data will be used by searchImageUrls in the database
  if (data.id_search) {
    // This enhances the id_search with file metadata when needed
    console.log(`Uploaded image ${originalFileName} (${originalFileSize} bytes) with ID: ${data.id_search}`);
    
    // You could attach metadata to the id_search string if needed
    // For now, we'll just return the plain id_search
    return data.id_search;
  }

  throw new Error('Invalid response from upload service');
}

/**
 * Get search results for a given search ID
 */
export async function getSearchResults(searchId: string, signal?: AbortSignal, captchaToken?: string): Promise<SearchResult[]> {
  let response;
  try {
    response = await fetch('/api/search?action=get-results', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id_search: searchId,
        with_progress: false,
        status_only: false,
        demo: true,
        captchaToken // Include CAPTCHA token for verification
      }),
      signal // Pass the abort signal to the fetch call
    });
    
    if (!response.ok) {
      // Check for specific 503 "Upstream service error" or 504 Gateway Timeout for retry
      if (response.status === 503 || response.status === 504) {
        try {
          // For 504, always retry regardless of error message
          if (response.status === 504) {
            console.warn(`[apiService] Received 504 Gateway Timeout for search ID: ${searchId}. Retrying...`);
            
            // Wait 5 seconds before retry
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Recursively retry the request
            return await getSearchResults(searchId, signal);
          }
          
          // For 503, check the specific error message
          const errorData = await response.clone().json();
          if (errorData && errorData.error === "Upstream service error") {
            console.warn(`[apiService] Received 503 Upstream service error for search ID: ${searchId}. Retrying...`);
            
            // Wait 5 seconds before retry
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Recursively retry the request
            return await getSearchResults(searchId, signal, captchaToken);
          }
        } catch (jsonError) {
          // If we can't parse the JSON but it's a 504, retry anyway
          if (response.status === 504) {
            console.warn(`[apiService] Received 504 Gateway Timeout for search ID: ${searchId}. Retrying despite JSON parse error...`);
            
            // Wait 5 seconds before retry
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Recursively retry the request
            return await getSearchResults(searchId, signal, captchaToken);
          }
          
          console.error(`[apiService] Failed to parse error body for search ID: ${searchId}:`, jsonError);
        }
      }
      
      throw new Error(`Failed to get search results: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    // If the error is from an aborted request (user cancelled), don't retry
    if (error instanceof DOMException && error.name === 'AbortError') {
      throw error;
    }
    
    // For other errors, propagate them
    throw new Error(`Failed to get search results: ${error instanceof Error ? error.message : String(error)}`);
  }

  const data = await response.json();

  if (data.error) {
    throw new Error(data.error);
  }

  // Keep the raw response data for saving to database
  const rawResults = data.output?.items || [];

  // Process and return the results
  return (rawResults).map((result: any) => ({
    id: result.guid || `result-${Math.random().toString(36).substr(2, 9)}`,
    confidence: result.score || 0,
    title: result.source_name || null,
    sourceUrl: result.url?.value || result.url || '',
    thumbnail: result.base64 || null,
    domain: extractDomain(result.url?.value || result.url || ''),
    // Save the original result data for database storage
    rawData: result
  }));
}

/**
 * Save search results to database
 */
export async function saveFinalResults(reportId: string, results: any[]): Promise<{
  success: boolean,
  uploadId?: number,
  expiresAt?: Date,
  status?: string
}> {
  console.log(`[apiService] Saving final results for report ${reportId} with ${results.length} results`);
  
  try {
    // Make a single attempt to save the results
    const response = await fetch(`/api/data?action=save-results&id=${reportId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ results })
    });

    if (!response.ok) {
      // Attempt to parse error data for more context
      let errorMessage = `Failed to save search results (HTTP ${response.status})`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch (parseError) {
        console.warn(`[apiService] Could not parse error response body for report ${reportId}`);
      }
      throw new Error(errorMessage);
    }

    const responseData = await response.json();
    console.log(`[apiService] Save successful for report ${reportId}, status: ${responseData.status || 'unknown'}`);

    return {
      success: responseData.success,
      uploadId: responseData.uploadId,
      expiresAt: responseData.expiresAt ? new Date(responseData.expiresAt) : undefined,
      status: responseData.status
    };
  } catch (error) {
    console.error(`[apiService] Error saving final results for report ${reportId}:`, error);
    // Re-throw the error to be handled by the caller
    throw error;
  }
}

/**
 * Get a search report by ID
 */
export async function getSearchReport(reportId: string): Promise<any> {
  const response = await fetch(`/api/search?action=get-report&id=${reportId}`);

  if (!response.ok) {
    throw new Error('Failed to fetch report');
  }

  return await response.json();
}

/**
 * Get a search report by report_id
 */
export async function getSearchReportById(identifier: string): Promise<any> {
  const response = await fetch(`/api/search?action=get-report-by-id&identifier=${identifier}`);

  if (!response.ok) {
    throw new Error('Failed to fetch report');
  }

  return await response.json();
}

/**
 * Create a payment intent
 */
export async function createPaymentIntent(email: string, metadata: any = {}): Promise<{ clientSecret: string }> {
  const response = await fetch('/api/core?action=create-payment-intent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email,
      metadata
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to create payment intent');
  }

  return await response.json();
}

/**
 * Start a search with payment verification
 */
export async function startSearch(id_search: string, clientSecret: string, email?: string, captchaToken?: string): Promise<{
  success: boolean;
  reportId: string;
  report_id: string;
  usedToken: boolean;
}> {
  const response = await fetch('/api/start-search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      id_search,
      clientSecret,
      guestEmailForReport: email,
      captchaToken,
      // Include the original searchImageUrls if needed by passing id_search as both
      // the main search ID and the full array for searchImageUrls
      searchImageUrls: [id_search] // Ensure searchImageUrls is always an array containing at least the main search ID
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to start search process');
  }

  return await response.json();
}

/**
 * Helper function to extract domain from URL
 */
export function extractDomain(url: string): string {
  try {
    const domain = new URL(url).hostname.replace('www.', '');
    return domain;
  } catch (error) {
    return 'unknown';
  }
}

/**
 * Create a search alert for new matches
 */
export async function createSearchAlert(searchId: string, email: string): Promise<boolean> {
  try {
    // Validate inputs
    if (!searchId || !email) {
      throw new Error('Search ID and email are required');
    }
    
    // Simple email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Make API request to create alert
    const response = await fetch('/api/search?action=create-alert', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        searchId,
        email,
        frequency: 'daily' // Default to daily alerts
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create alert');
    }

    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('Error creating search alert:', error);
    throw error;
  }
}

/**
 * Export results as CSV
 */
export function exportResultsAsCSV(results: SearchResult[]): void {
  if (!results || results.length === 0) {
    console.error('No results to export');
    return;
  }

  try {
    // Define CSV headers
    const headers = [
      'ID',
      'Title',
      'Confidence',
      'URL',
      'Domain'
    ].join(',');

    // Create CSV rows
    const csvRows = results.map(result => {
      return [
        result.id,
        result.title || 'Unknown',
        `${Math.round(result.confidence)}%`,
        result.sourceUrl,
        extractDomain(result.sourceUrl)
      ].map(value => `"${String(value).replace(/"/g, '""')}"`).join(',');
    });

    // Combine headers and rows
    const csvContent = `${headers}\n${csvRows.join('\n')}`;

    // Create a Blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `facetrace-export-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error exporting results as CSV:', error);
  }
}
