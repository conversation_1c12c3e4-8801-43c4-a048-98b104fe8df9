import { Metadata } from 'next';
import { CaptchaTest } from '@/components/CaptchaTest';

export const metadata: Metadata = {
  title: 'reCAPTCHA Enterprise Test - FaceTrace',
  description: 'Test page for reCAPTCHA Enterprise integration',
  robots: 'noindex, nofollow', // Don't index this test page
};

/**
 * Test page for reCAPTCHA Enterprise integration
 * Access this page at /captcha-test to verify CAPTCHA functionality
 */
export default function CaptchaTestPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            reCAPTCHA Enterprise Test
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Use this page to test the reCAPTCHA Enterprise integration
          </p>
        </div>
        
        <CaptchaTest />
        
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            This is a test page for development purposes only
          </p>
        </div>
      </div>
    </div>
  );
}
