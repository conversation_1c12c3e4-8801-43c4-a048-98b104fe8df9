import { WebhookEvent } from '@clerk/nextjs/server';
import { headers } from 'next/headers';
import { NextResponse } from 'next/server';
import { Webhook } from 'svix';

import { db } from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

/**
 * Webhook handler for Clerk user management events
 * 
 * Processes the following events:
 * - user.created: Creates a new user in our database
 * - user.updated: Updates user information in our database
 * - user.deleted: Soft-deletes the user in our database
 */
export async function POST(req: Request) {
  try {
    // Get the headers
    const headersList = await headers();
    const svix_id = headersList.get('svix-id');
    const svix_timestamp = headersList.get('svix-timestamp');
    const svix_signature = headersList.get('svix-signature');

    // If there are no Svix headers, error out
    if (!svix_id || !svix_timestamp || !svix_signature) {
      return new Response('Missing Svix headers', {
        status: 400,
      });
    }

    // Get the body
    const payload = await req.json();
    const body = JSON.stringify(payload);

    // Create a new Svix instance with our webhook secret
    const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('CLERK_WEBHOOK_SECRET is not defined');
      return new Response('Webhook secret not configured', {
        status: 500,
      });
    }

    const wh = new Webhook(webhookSecret);

    let evt: WebhookEvent;

    // Verify the webhook
    try {
      evt = wh.verify(body, {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature,
      }) as WebhookEvent;
    } catch (err) {
      console.error('Error verifying webhook:', err);
      return new Response('Error verifying webhook', {
        status: 400,
      });
    }

    // Process the webhook based on event type
    const eventType = evt.type;
    
    switch (eventType) {
      case 'user.created': {
        await handleUserCreated(evt.data);
        break;
      }
      case 'user.updated': {
        await handleUserUpdated(evt.data);
        break;
      }
      case 'user.deleted': {
        await handleUserDeleted(evt.data);
        break;
      }
      default:
        console.log(`Unhandled webhook event type: ${eventType}`);
    }
    
    return NextResponse.json(
      { success: true, message: `Webhook processed: ${eventType}` },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error processing webhook:`, error);
    return NextResponse.json(
      { success: false, message: 'Error processing webhook' },
      { status: 500 }
    );
  }
}

/**
 * Handle user.created event
 * 
 * Creates a new user in our database and sets up any necessary default settings
 */
async function handleUserCreated(data: any) {
  console.log(`[WEBHOOK DEBUG] handleUserCreated called with data:`, JSON.stringify(data, null, 2));
  
  const { id, email_addresses, first_name, last_name } = data;
  
  // Get the primary email
  const primaryEmail = email_addresses?.find((email: any) => 
    email.id === data.primary_email_address_id
  )?.email_address;
  
  if (!primaryEmail) {
    console.error(`[WEBHOOK ERROR] No primary email found for user ${id}`);
    return;
  }

  console.log(`[WEBHOOK DEBUG] Processing user.created for ${id} with email ${primaryEmail}`);
  
  try {
    // Import Stripe and create a customer if needed
    const stripeEnabled = !!(process.env.STRIPE_SECRET_KEY);
    let stripeCustomerId = null;
    
    console.log(`[WEBHOOK DEBUG] Stripe integration enabled: ${stripeEnabled}`);
    
    if (stripeEnabled) {
      try {
        console.log(`[WEBHOOK DEBUG] Importing Stripe library...`);
        const Stripe = require('stripe');
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
          apiVersion: '2025-02-24.acacia',
        });
        
        // Create a Stripe customer
        console.log(`[WEBHOOK DEBUG] Creating Stripe customer for ${id}...`);
        const customer = await stripe.customers.create({
          email: primaryEmail,
          name: `${first_name || ''} ${last_name || ''}`.trim() || undefined,
          metadata: {
            clerkUserId: id,
          },
        });
        
        stripeCustomerId = customer.id;
        console.log(`[WEBHOOK DEBUG] Successfully created Stripe customer for user ${id}: ${stripeCustomerId}`);
        
        // Update Clerk user metadata with Stripe customer ID
        console.log(`[WEBHOOK DEBUG] Updating Clerk user metadata with Stripe customer ID...`);
        try {
          const { clerkClient } = require('@clerk/clerk-sdk-node');
          // Use the clerkClient to update user metadata
          await clerkClient.users.updateUser(id, {
            privateMetadata: {
              stripeCustomerId: customer.id,
            },
          });
          console.log(`[WEBHOOK DEBUG] Successfully updated Clerk metadata for user ${id}`);
        } catch (clerkErr) {
          console.error(`[WEBHOOK ERROR] Failed to update Clerk metadata:`, clerkErr);
        }
      } catch (err) {
        console.error(`[WEBHOOK ERROR] Error creating Stripe customer for user ${id}:`, err);
        // Continue without Stripe - we don't want to block user creation
      }
    }
    
    // Create user in database
    console.log(`[WEBHOOK DEBUG] Preparing database insertion for user ${id}...`);
    const newUser = {
      clerkId: id,
      email: primaryEmail,
      name: `${first_name || ''} ${last_name || ''}`.trim() || null,
      verified: true,
      tokens: 5,
      initialFreeUnlockUsed: false,
      stripeCustomerId: stripeCustomerId,
    };
    
    console.log(`[WEBHOOK DEBUG] Attempting to insert user into DB:`, JSON.stringify(newUser));
    
    try {
      console.log(`[WEBHOOK DEBUG] Database connection status:`, db ? 'Connection available' : 'No connection');
      await db.insert(schema.users).values(newUser);
      console.log(`[WEBHOOK DEBUG] Successfully created user in DB with Clerk ID: ${id}`);
    } catch (dbError: any) {
      console.error(`[WEBHOOK ERROR] Database insertion failed:`, dbError);
      // Throw the error to be caught by the outer try/catch block
      throw new Error(`Database insertion failed: ${dbError?.message || 'Unknown database error'}`);
    }
  } catch (error) {
    console.error(`Error creating user ${id} in database:`, error);
    throw error;
  }
}

/**
 * Handle user.updated event
 * 
 * Updates user information in our database
 */
async function handleUserUpdated(data: any) {
  const { id, email_addresses, first_name, last_name } = data;
  
  // Get the primary email
  const primaryEmail = email_addresses?.find((email: any) => 
    email.id === data.primary_email_address_id
  )?.email_address;
  
  if (!primaryEmail) {
    console.error(`No primary email found for user ${id}`);
    return;
  }

  try {
    // First check if the user exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.clerkId, id),
    });

    if (!existingUser) {
      console.log(`User ${id} not found in database, creating new record`);
      // Create the user if they don't exist (might happen if webhooks are set up after users are created)
      return await handleUserCreated(data);
    }

    // Update user in database
    await db
      .update(schema.users)
      .set({
        email: primaryEmail,
        name: `${first_name || ''} ${last_name || ''}`.trim() || null,
        updatedAt: new Date(),
      })
      .where(sql`${schema.users.clerkId} = ${id}`);
    
    console.log(`Updated user in DB with Clerk ID: ${id}`);
  } catch (error) {
    console.error(`Error updating user in database:`, error);
    throw error;
  }
}

/**
 * Handle user.deleted event
 * 
 * Soft-deletes the user in our database by removing personal information 
 * but preserving historical data
 */
async function handleUserDeleted(data: any) {
  const { id } = data;

  try {
    // Check if the user exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.clerkId, id),
    });

    if (!existingUser) {
      console.log(`User ${id} not found in database, nothing to delete`);
      return;
    }

    // Since we don't have a 'deleted' flag in our schema, we'll anonymize the user data
    // but preserve the relationship to historical data
    await db
      .update(schema.users)
      .set({
        email: null, // Remove email to maintain privacy
        name: null, // Remove name for privacy
        updatedAt: new Date(),
      })
      .where(sql`${schema.users.clerkId} = ${id}`);

    console.log(`Soft-deleted user with Clerk ID: ${id}`);
  } catch (error) {
    console.error(`Error soft-deleting user in database:`, error);
    throw error;
  }
}
