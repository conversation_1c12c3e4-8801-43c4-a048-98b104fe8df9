import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { 
  getSearchReportById, 
  createGuestTransaction, 
  createReportTransaction,
  updateSearchReport,
  getReportTransactionByPaymentId
} from '@/lib/db/index';
import { stripe } from '@/lib/stripe';
import { sendSearchReportEmail } from '@/lib/emails';

export async function POST(req: NextRequest) {
  try {
    const { paymentIntentId, reportId, email } = await req.json();

    // Basic validation
    if (!reportId || !paymentIntentId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Retrieve the payment intent from Stripe to verify payment
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    // Check if payment was successful
    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json({ 
        error: 'Payment not completed',
        status: paymentIntent.status 
      }, { status: 400 });
    }
    
    // Get the search report to unlock
    const report = await getSearchReportById(reportId);
    
    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // Check if a transaction for this payment already exists
    const existingTransaction = await getReportTransactionByPaymentId(paymentIntentId);
    if (existingTransaction) {
      return NextResponse.json({ 
        error: 'This payment has already been processed',
        reportId: report.report_id 
      }, { status: 400 });
    }

    // Get auth session
    const { userId: clerkId } = auth();
    
    // Record the transaction based on user type
    if (clerkId) {
      // Registered user flow
      // Find the user's ID in our database (this would be implemented in a real app)
      // For  purposes we'll use a placeholder
      const userId = 1; // This would be looked up from the clerkId
      
      await createReportTransaction({
        userId, 
        reportId: report.id,
        amount: String(report.price ? parseFloat(report.price) : 0),
        status: 'completed',
        currency: 'usd',
        stripePaymentId: paymentIntentId,
        metadata: { 
          source: 'unlock_search', 
          service: 'face_search',
          clerkId
        }
      });
    } else {
      // Guest user flow
      await createGuestTransaction({
        email: email || report.email,
        reportId: report.id,
        amount: String(report.price ? parseFloat(report.price) : 0),
        status: 'completed',
        currency: 'usd',
        stripePaymentId: paymentIntentId,
        metadata: { 
          source: 'unlock_search', 
          service: 'face_search'
        }
      });
    }
    
    // Update the report status to unlocked/completed
    await updateSearchReport(report.id, {
      status: 'completed', // Change this status as needed
      stripe_pm_id: paymentIntentId,
    });
    
    // Send confirmation email to guest users
    if (!clerkId && email) {
      try {
        await sendSearchReportEmail(email, {
          reportId: report.report_id,
          resultCount: report.resultCount,
          previewUrls: report.searchImageUrls as string[],
        });
      } catch (emailError) {
        console.error('Failed to send email:', emailError);
        // We don't want to fail the whole request if just the email fails
      }
    }
    
    return NextResponse.json({ 
      success: true, 
      reportId: report.report_id,
      url: `/r/${report.report_id}`
    });
    
  } catch (error) {
    console.error('Error unlocking search:', error);
    return NextResponse.json({ error: 'Failed to unlock search' }, { status: 500 });
  }
} 