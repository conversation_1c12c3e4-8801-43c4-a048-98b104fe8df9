// src/app/api/search/route.ts
import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { Stripe } from 'stripe';
import { verifyCaptcha, getClientIP } from '@/lib/captcha';
import {
  createSearchReport,
  updateSearchReport,
  getSearchReportById,
  getSearchReportByReportId,
  createGuestTransaction,
  createUpload,
  getUploadsBySearchReportId
} from '@/lib/db';
import { Resend } from 'resend';
import { eq } from 'drizzle-orm';
import * as schema from '@/lib/db/schema';
import { db } from '@/lib/db';

// Initialize Resend for email
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

// FaceCheck API configuration
const FACECHECK_API_BASE_URL = process.env.FACECHECK_API_URL || 'https://facecheck.id';
const FACECHECK_API_TOKEN = process.env.FACECHECK_API_KEY || 'PVP7q4Otamgc3SW+cGUIx5ce4nRe5P4k7ZbN4m7Z9aYlmoyulnQ+Ji5eDySlcYT0F8JE0E8DEVQ=';
const DEMO_MODE = process.env.DEMO_MODE || true;

// Add the SearchResult interface
interface SearchResult {
  id: string;
  confidence: number;
  sourceUrl?: string;
  sourceType?: string;
  thumbnail?: string;
  domain?: string;
  [key: string]: any; // Allow for other properties
}

/**
 * Main handler for search API requests
 */
export async function POST(request: NextRequest) {
  try {
    // Check for search data format according to OpenAPI spec
    // If the request is in JSON format (regular search), use that
    const contentType = request.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      // This is a standard search request as per the OpenAPI spec
      // We just need to forward to the FaceCheck API
      return handleSearchRequest(request);
    } else {
      // Parse action from searchParams for our internal API extensions
      const { searchParams } = new URL(request.url);
      const action = searchParams.get('action');
      const id = searchParams.get('id');

      // Handle different actions
      switch (action) {
        case 'upload':
          return handleUpload(request);
        case 'get-results':
          return id ? handleGetResults(request, id) : NextResponse.json({ error: 'Missing report ID' }, { status: 400 });
        case 'get-report':
          return id ? handleGetReport(request, id) : NextResponse.json({ error: 'Missing report ID' }, { status: 400 });
        case 'get-report-by-id':
          return handleGetReportById(request);
        case 'create-alert':
          return handleCreateAlert(request);
        default:
          return NextResponse.json({ error: 'Invalid action or missing content-type' }, { status: 400 });
      }
    }
  } catch (error) {
    console.error('Error in search API:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}

/**
 * Handle standard search requests (JSON format) as per OpenAPI spec
 */
async function handleSearchRequest(request: NextRequest) {
  const logPrefix = `[handleSearchRequest]`;
  
  try {
    let searchData;
    try {
      searchData = await request.json();
      console.log(`${logPrefix} Parsed JSON request body.`);
    } catch (parseError: any) {
      console.error(`${logPrefix} Error parsing request JSON:`, parseError);
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    const targetUrl = `${FACECHECK_API_BASE_URL}/api/search`;
    console.log(`${logPrefix} Forwarding request to FaceCheck: ${targetUrl}`);

    try {
      // Make the request to FaceCheck API with incremental retry
      let attempts = 0;
      const maxAttempts = 2;
      let lastError;
      
      while (attempts < maxAttempts) {
        try {
          attempts++;
          console.log(`${logPrefix} Making attempt ${attempts}/${maxAttempts} to FaceCheck API`);
          
          const faceCheckResponse = await axios.post(targetUrl, searchData, {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': FACECHECK_API_TOKEN,
            },
            timeout: 30000, // 30 second timeout
            params: {
              demo: false,
              cached: false
            }
          });
          
          console.log(`${logPrefix} Received successful response from FaceCheck on attempt ${attempts}`);
          
          // Return the response on success
          return NextResponse.json(faceCheckResponse.data);
          
        } catch (retryError: any) {
          lastError = retryError;
          
          // Only retry on timeout errors
          if (retryError.code !== 'ECONNABORTED' && !retryError.message?.includes('timeout')) {
            console.log(`${logPrefix} Non-timeout error, not retrying:`, retryError.message);
            throw retryError; // Don't retry non-timeout errors
          }
          
          console.log(`${logPrefix} FaceCheck API request timed out, attempt ${attempts}/${maxAttempts}`);
          
          // If this was the last attempt, let it fall through to the error handler
          if (attempts >= maxAttempts) {
            console.log(`${logPrefix} Maximum retry attempts (${maxAttempts}) reached`);
            throw retryError;
          }
          
          // Wait before retrying (1 second)
          console.log(`${logPrefix} Waiting 1 second before retry`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      // If we somehow get here without returning or throwing, use the last error
      throw lastError;
      
    } catch (axiosError: any) {
      if (axios.isAxiosError(axiosError)) {
        if (axiosError.response?.status === 520 && request.url.includes('action=save-results')) {
          const { searchParams } = new URL(request.url);
          const reportId = searchParams.get('id');
          
          if (reportId && searchData.results) {
            try {
              // Process results to reduce size before saving
              const processedResults = searchData.results.map((result: SearchResult) => ({
                id: result.id,
                confidence: result.confidence,
                sourceUrl: result.sourceUrl,
                sourceType: result.sourceType,
                thumbnail: result.thumbnail,
                domain: result.domain,
                // Trim any other large fields as needed
              }));
              
              // Save the report results
              const resultsJson = JSON.stringify(processedResults);
              const expiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000));

              // Use direct db update for more reliability
              const updatedReport = await db.update(schema.searchReports)
                .set({
                  facecheckIdSearch: resultsJson,
                  status: 'completed',
                  progress: 100,
                  resultCount: searchData.results.length,
                  expiresAt: expiresAt,
                  updatedAt: new Date()
                })
                .where(eq(schema.searchReports.id, parseInt(reportId)))
                .returning();

              console.log(`[handleSearchRequest] Search report ${reportId} updated successfully with ${searchData.results.length} results. Status: ${updatedReport[0]?.status || 'unknown'}`);
            } catch (dbError) {
              console.error(`[handleSearchRequest] Error updating search report ${reportId}:`, dbError);
            }
          }
        }
        
        // Handle other error cases or when save-results handling fails
        console.error(`${logPrefix} Error during Axios request to FaceCheck:`, axiosError.message);
        return NextResponse.json({
          error: 'Upstream service error',
          message: 'The FaceCheck API service encountered an error.',
          upstreamStatus: axiosError.response?.status || 500
        }, { status: 503 });
      }
    }
  } catch (error) {
    console.error('Error in search API:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}

/**
 * Handle image upload to FaceCheck API
 * This directly forwards the multipart/form-data to the FaceCheck API without modifying it
 */
async function handleUpload(request: NextRequest) {
  try {
    // Check if FaceCheck API key is configured
    if (!FACECHECK_API_TOKEN) {
      return NextResponse.json({ error: 'FaceCheck API Key not configured' }, { status: 500 });
    }

    // Get form data from request
    const formData = await request.formData();
    
    // Get the image file from the form data
    const imageFile = formData.get('images') as File;
    if (!imageFile || !(imageFile instanceof File)) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }
    
    // Forward the request to FaceCheck API
    const targetUrl = `${FACECHECK_API_BASE_URL}/api/upload_pic`;
    
    // Create a new FormData object to send to FaceCheck
    const faceCheckFormData = new FormData();
    
    // Copy all fields from the original request
    for (const [key, value] of formData.entries()) {
      faceCheckFormData.append(key, value);
    }
    
    try {
      // Make the request to FaceCheck API
      const faceCheckResponse = await axios.post(targetUrl, faceCheckFormData, {
        headers: {
          'Accept': 'application/json',
          'Authorization': FACECHECK_API_TOKEN,
          // Let axios set the correct content-type with boundary for multipart/form-data
        },
        timeout: 60000, // 60 second timeout for uploads, as they can take longer

      });
      
      // Prepare the response with blob data for searchImageUrls
      const responseData = faceCheckResponse.data;
      
      // Extract the report ID if provided in the search params
      const { searchParams } = new URL(request.url);
      const reportId = searchParams.get('reportId');
      
      // If a report ID is provided, update the search report with image data
      if (reportId) {
        try {
          const report = await getSearchReportById(reportId);
          if (report) {
            // Create image data to store - use input data from the upload instead of output
            const blobData = {
              // Keep the image metadata
              file_name: imageFile.name,
              file_type: imageFile.type, 
              file_size: imageFile.size,
              // Store any relevant data from the response
              id_search: responseData.id_search,
              input: responseData.input
            };
            
            // Update the report with the searchImageUrls data
            await updateSearchReport(report.id, {
              searchImageUrls: blobData
            });
            
            console.log(`[handleUpload] Updated report ${reportId} with search image data`);
          }
        } catch (updateError) {
          console.error(`[handleUpload] Failed to update report ${reportId} with image data:`, updateError);
          // Continue despite error - we'll return the API response anyway
        }
      }
      
      // Return the response with additional blob info
      return NextResponse.json({
        ...responseData,
        // Include additional data if needed for blob handling
        file_name: imageFile.name,
        file_type: imageFile.type,
        file_size: imageFile.size
      });
    } catch (axiosError: any) {
      // Check specifically for timeout errors
      if (axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout')) {
        console.error('FaceCheck API upload timed out:', axiosError.message);
        return NextResponse.json({ 
          error: 'FaceCheck API upload timed out', 
          message: 'The upload is taking longer than expected. Please try again with a smaller image.',
          code: 'TIMEOUT'
        }, { status: 504 }); // Gateway Timeout
      }
      
      // Re-throw for other axios errors to be handled by the outer catch
      throw axiosError;
    }
  } catch (error: any) {
    console.error('Error uploading image:', error);
    
    // Handle axios errors
    if (error.response) {
      // The request was made and the server responded with a status code outside of 2xx
      return NextResponse.json({ 
        error: 'Error from FaceCheck API', 
        details: error.response.data,
        status: error.response.status 
      }, { status: error.response.status });
    } else if (error.request) {
      // The request was made but no response was received
      return NextResponse.json({ 
        error: 'No response from FaceCheck API', 
        details: 'The upload service is currently unavailable' 
      }, { status: 503 });
    } else {
      // Something happened in setting up the request
      return NextResponse.json({ 
        error: 'Error setting up request', 
        details: error.message 
      }, { status: 500 });
    }
  }
}

/**
 * Get search results from FaceCheck API
 */
async function handleGetResults(request: NextRequest, reportId: string) {
  try {
    // Parse request body for additional parameters and CAPTCHA token
    const { with_progress = false, status_only = false, captchaToken } = await request.json();

    // --- CAPTCHA Verification ---
    // CAPTCHA verification is ALWAYS required for search progress requests
    if (!captchaToken) {
      return NextResponse.json({
        error: 'CAPTCHA verification is required. Please complete the security check.'
      }, { status: 400 });
    }

    const clientIP = getClientIP(request);
    const captchaResult = await verifyCaptcha(captchaToken, clientIP, 'FACETRACE_SEARCH_PROGRESS');

    if (!captchaResult.success) {
      console.warn(`[search] CAPTCHA verification failed for ${clientIP}: ${captchaResult.error}`);
      return NextResponse.json({
        error: captchaResult.error || 'CAPTCHA verification failed. Please try again.'
      }, { status: 400 });
    }

    console.log(`[search] CAPTCHA verification successful for ${clientIP} (score: ${captchaResult.score || 'N/A'})`);

    // Get the report
    const report = await getSearchReportById(reportId);

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Get the search ID from the report
    const searchId = report.facecheckIdSearch;

    if (!searchId) {
      return NextResponse.json({ error: 'Search ID not found in report' }, { status: 400 });
    }
    
    // Forward the request to FaceCheck API
    const targetUrl = `${FACECHECK_API_BASE_URL}/api/search`;
    
    try {
      // Make the request to FaceCheck API with incremental retry
      let attempts = 0;
      const maxAttempts = 2;
      let lastError;
      
      while (attempts < maxAttempts) {
        try {
          attempts++;
          const faceCheckResponse = await axios.post(targetUrl, {
            id_search: searchId,
            with_progress,
            status_only,
            demo: false,
            cached: false
          }, {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': 'PVP7q4Otamgc3SW+cGUIx5ce4nRe5P4k7ZbN4m7Z9aYlmoyulnQ+Ji5eDySlcYT0F8JE0E8DEVQ=',
            
            },
            timeout: 30000, // 30 second timeout

          });
          
          // Return the response from FaceCheck on success
          return NextResponse.json(faceCheckResponse.data);
        } catch (retryError: any) {
          lastError = retryError;
          
          // Only retry on timeout errors
          if (retryError.code !== 'ECONNABORTED' && !retryError.message?.includes('timeout')) {
            throw retryError; // Don't retry non-timeout errors
          }
          
          console.log(`FaceCheck API results request timed out, attempt ${attempts}/${maxAttempts}`);
          
          // If this was the last attempt, let it fall through to the error handler
          if (attempts >= maxAttempts) {
            throw retryError;
          }
          
          // Wait before retrying (500ms)
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      // If we get here, all attempts failed
      throw lastError;
    } catch (axiosError: any) {
      // Check specifically for timeout errors
      if (axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout')) {
        console.error('FaceCheck API results request timed out after all retries:', axiosError.message);
        return NextResponse.json({ 
          error: 'FaceCheck API request timed out', 
          message: 'The search results are taking longer than expected. Please try again later.',
          code: 'TIMEOUT'
        }, { status: 504 }); // Gateway Timeout
      }
      
      // Re-throw for other axios errors to be handled by the outer catch
      throw axiosError;
    }
  } catch (error: any) {
    console.error('Error getting search results:', error);
    
    // Handle axios errors
    if (error.response) {
      return NextResponse.json({ 
        error: 'Error from FaceCheck API', 
        details: error.response.data,
        status: error.response.status 
      }, { status: error.response.status });
    } else if (error.request) {
      return NextResponse.json({ 
        error: 'No response from FaceCheck API', 
        details: 'The search service is currently unavailable' 
      }, { status: 503 });
    } else {
      return NextResponse.json({ 
        error: 'Error setting up request', 
        details: error.message 
      }, { status: 500 });
    }
  }
}

/**
 * Get report by ID
 */
async function handleGetReport(request: NextRequest, reportId: string) {
  try {
    console.log(`[search-api] Fetching report details for ID: ${reportId}`);
    
    // Get the report
    const report = await getSearchReportById(reportId);
    
    if (!report) {
      console.warn(`[search-api] Report not found for ID: ${reportId}`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // Get uploads for the report (only if status indicates completion)
    let uploads: any[] = [];
    try {
      if (report.status === 'completed') {
        console.log(`[search-api] Report ${reportId} is complete. Fetching uploads.`);
        uploads = await getUploadsBySearchReportId(report.id);
        console.log(`[search-api] Found ${uploads.length} uploads for report ${reportId}.`);
      } else {
        console.log(`[search-api] Report ${reportId} status is ${report.status}. Not fetching uploads yet.`);
      }
    } catch (uploadError) {
      console.error(`[search-api] Error fetching uploads for report ${reportId}:`, uploadError);
      // We'll continue without uploads if there was an error fetching them
      // This ensures the report data is still returned even if there are upload issues
    }
    
    // Return the report data including status, progress, and uploads if ready
    return NextResponse.json({
      id: report.id,
      status: report.status,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
      resultCount: report.resultCount,
      isPrivate: report.isPrivate,
      progress: report.progress || 0,
      uploads: uploads,
      facecheckIdSearch: report.facecheckIdSearch,
      // Only include stripe_pm_id in the response if it exists
      ...(report.stripe_pm_id ? { stripe_pm_id: report.stripe_pm_id } : {})
    });
  } catch (error) {
    console.error(`[search-api] Error fetching report ${reportId}:`, error);
    return NextResponse.json({ error: 'Error fetching report' }, { status: 500 });
  }
}

/**
 * Get report by report_id
 */
async function handleGetReportById(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const identifier = searchParams.get('identifier');
    
    if (!identifier) {
      return NextResponse.json({ error: 'Report identifier is required' }, { status: 400 });
    }
    
    console.log('[search-api] Fetching report details for identifier:', identifier);
    
    // Use the helper function to get the report by report_id
    const report = await getSearchReportByReportId(identifier);
    
    if (!report) {
      console.warn('[search-api] Report not found for identifier:', identifier);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // Get uploads for the report (only if status indicates completion)
    let uploads: any[] = [];
    try {
      if (report.status === 'completed') {
        console.log('[search-api] Report is complete. Fetching uploads.');
        uploads = await getUploadsBySearchReportId(report.id);
        console.log('[search-api] Found', uploads.length, 'uploads for report.');
      } else {
        console.log('[search-api] Report status is', report.status, '. Not fetching uploads yet.');
      }
    } catch (uploadError) {
      console.error('[search-api] Error fetching uploads for report:', uploadError);
      // Continue without uploads if there was an error
    }
    
    // Return the report data including status, progress, and uploads if ready
    return NextResponse.json({
      id: report.id,
      status: report.status,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
      resultCount: report.resultCount,
      isPrivate: report.isPrivate,
      progress: report.progress || 0,
      expiresAt: report.expiresAt,
      uploads: uploads,
      facecheckIdSearch: report.facecheckIdSearch,
      // Only include stripe_pm_id if it exists
      ...(report.stripe_pm_id ? { stripe_pm_id: report.stripe_pm_id } : {})
    });
  } catch (error) {
    console.error('[search-api] Error fetching report by identifier:', error);
    return NextResponse.json({ error: 'Error fetching report' }, { status: 500 });
  }
}

/**
 * Create a search alert
 * 
 * This is a simplified implementation since we don't have a dedicated alerts table.
 * In a real implementation, you would create a proper alerts table in the database.
 */
async function handleCreateAlert(request: NextRequest) {
  const logPrefix = '[create-alert]';
  
  try {
    // Parse the request body
    const { searchId, email, frequency = 'daily' } = await request.json();
    
    if (!searchId) {
      return NextResponse.json({ error: 'Search ID is required' }, { status: 400 });
    }
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }
    
    console.log(`${logPrefix} Creating alert for search ${searchId} with email ${email}`);
    
    // Check if the report exists
    let report = null;
    
    // Try to find report by reportId first
    try {
      report = await getSearchReportByReportId(searchId);
    } catch (error) {
      console.error(`${logPrefix} Error finding report by report_id:`, error);
    }
    
    // If not found by report_id, try by numeric ID
    if (!report && !isNaN(parseInt(searchId))) {
      try {
        report = await getSearchReportById(searchId);
      } catch (error) {
        console.error(`${logPrefix} Error finding report by numeric ID:`, error);
      }
    }
    
    if (!report) {
      console.warn(`${logPrefix} Report not found for ID: ${searchId}`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // In a production environment, we would store this in an alerts table
    // For this implementation, we'll send a confirmation email if configured
    // but won't actually store the alert since we don't have a metadata field
    
    console.log(`${logPrefix} Processing alert request for email ${email}`);
    
    // Send confirmation email if Resend is configured
    if (resend) {
      try {
        await resend.emails.send({
          from: process.env.RESEND_SENDER_EMAIL || '<EMAIL>',
          to: email,
          subject: 'Face Match Alert Confirmation',
          html: `
            <h1>Alert Confirmation</h1>
            <p>You are now subscribed to receive alerts for new face matches.</p>
            <p>We'll notify you when new potential matches are found for your search.</p>
            <p>Search ID: ${searchId}</p>
            <p>Frequency: ${frequency}</p>
          `
        });
        console.log(`${logPrefix} Confirmation email sent to ${email}`);
      } catch (emailError) {
        console.error(`${logPrefix} Error sending confirmation email:`, emailError);
        return NextResponse.json({ 
          success: false, 
          message: 'Failed to send confirmation email' 
        }, { status: 500 });
      }
    } else {
      console.log(`${logPrefix} Resend not configured, skipping confirmation email`);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Alert request processed successfully'
    });
  } catch (error) {
    console.error('[create-alert] Unexpected error:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
