import { NextRequest, NextResponse } from 'next/server';
import { verifyCaptcha, getClientIP } from '@/lib/captcha';

/**
 * Test endpoint for reCAPTCHA Enterprise verification
 * This endpoint can be used to test the CAPTCHA backend integration
 */
export async function POST(request: NextRequest) {
  try {
    const { captchaToken, action = 'TEST_ACTION' } = await request.json();

    // Validate input
    if (!captchaToken) {
      return NextResponse.json({
        success: false,
        error: 'CAPTCHA token is required'
      }, { status: 400 });
    }

    // Get client IP for verification
    const clientIP = getClientIP(request);
    
    console.log('[test-captcha] Testing CAPTCHA verification:', {
      action,
      clientIP,
      tokenLength: captchaToken.length,
      tokenPrefix: captchaToken.substring(0, 20) + '...'
    });

    // Verify CAPTCHA token
    const captchaResult = await verifyCaptcha(captchaToken, clientIP, action);

    // Log the result
    console.log('[test-captcha] Verification result:', {
      success: captchaResult.success,
      score: captchaResult.score,
      action: captchaResult.action,
      error: captchaResult.error
    });

    // Return detailed result for testing
    return NextResponse.json({
      success: captchaResult.success,
      score: captchaResult.score,
      action: captchaResult.action,
      error: captchaResult.error,
      clientIP: clientIP,
      timestamp: new Date().toISOString(),
      environment: {
        nodeEnv: process.env.NODE_ENV,
        projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
        siteKey: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY?.substring(0, 20) + '...',
        forceStandard: process.env.FORCE_STANDARD_RECAPTCHA,
        hasApiKey: !!process.env.GOOGLE_CLOUD_API_KEY,
        hasSecretKey: !!process.env.RECAPTCHA_SECRET_KEY
      }
    });

  } catch (error) {
    console.error('[test-captcha] Error processing request:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error during CAPTCHA verification',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to check configuration
 */
export async function GET() {
  return NextResponse.json({
    message: 'reCAPTCHA Enterprise Test Endpoint',
    configuration: {
      nodeEnv: process.env.NODE_ENV,
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
      siteKey: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY?.substring(0, 20) + '...',
      forceStandard: process.env.FORCE_STANDARD_RECAPTCHA,
      hasApiKey: !!process.env.GOOGLE_CLOUD_API_KEY,
      hasSecretKey: !!process.env.RECAPTCHA_SECRET_KEY
    },
    usage: 'POST with { captchaToken, action } to test verification'
  });
}
