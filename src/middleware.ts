import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getClientIP } from "./lib/captcha";

// Check if authentication is disabled via environment variable
const isAuthDisabled = process.env.DISABLE_AUTH === 'true';

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  // Allow search page as public for guest usage
  '/',
  '/search',
  '/r/:path*', // Report viewing routes
  // Static pages
  '/about',
  '/contact',
  '/legal',
  '/privacy-policy',
  '/terms-of-use',
  '/faq',
  '/dmca-takedown',
  '/opt-out',
  '/refund-policy',
  '/report-bug',
  '/sar-form',
  '/support',
  // API routes that handle their own auth
  '/api/webhooks/:path*', // Webhook endpoints
  '/api/start-search',    // Search start endpoint
  '/api/search',          // Search progress/results endpoint
  // Static assets and resources
  '/_next/static/:path*',
  '/images/:path*',
  '/api/health', // Health check endpoint
]);

// Security middleware for user agent filtering and validation
function securityMiddleware(req: NextRequest): NextResponse | null {
  const { pathname } = req.nextUrl;

  // Basic security headers and validation
  const userAgent = req.headers.get('user-agent') || '';

  // Block obvious bots and suspicious user agents
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /requests/i
  ];

  return null; // Continue processing
}

// Enhanced clerk middleware with custom logic
export default clerkMiddleware((auth, req) => {
  // SECURITY: Apply security middleware FIRST and ALWAYS, regardless of auth status
  // This ensures user agent filtering and other security measures are always active
  // CAPTCHA verification is now handled at the API endpoint level for better granular control
  const securityResponse = securityMiddleware(req);
  if (securityResponse) {
    return securityResponse;
  }

  // If auth is disabled, allow all routes but security middleware has already run
  if (isAuthDisabled) {
    console.log('[middleware] Authentication disabled - allowing all routes (security middleware already applied)');
    return NextResponse.next();
  }

  // Check if this is a public route
  if (isPublicRoute(req)) {
    return NextResponse.next();
  }

  // Let Clerk handle the auth for all routes
  // No need to manually check auth status here as Clerk will do this
  return NextResponse.next();
});

// This configuration ensures the middleware runs on the specified paths
// The negative lookahead pattern excludes certain paths from the middleware
export const config = {
  matcher: [
    // Skip Next.js internals, Clerk auth pages, and static files
    '/((?!_next|_clerk|clerk-proxy|sign-in|sign-up|images|favicon|api/webhooks).*)',
    // Always run middleware for API routes except webhooks
    '/(api(?!/webhooks))(.*)',
  ],
};
